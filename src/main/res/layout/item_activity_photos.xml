<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:id="@+id/layout_rl"
    android:orientation="vertical"
    android:gravity="center">
    <ImageView
        android:id="@+id/photoView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/bar_grey"
        android:scaleType="centerCrop"
        android:layout_centerInParent="true" />

    <ImageView
        android:id="@+id/iv_tempView"
        android:layout_width="150dp"
        android:layout_height="150dp"
        android:scaleType="fitXY"
        android:layout_centerInParent="true"
        />

    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:visibility="gone"/>
</RelativeLayout>