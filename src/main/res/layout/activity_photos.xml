<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/bar_grey"
    android:orientation="vertical">
    <com.leeson.image_pickers.views.ViewPagerFixed
        android:id="@+id/viewPager"
        android:background="@color/bar_grey"
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>

    <LinearLayout
        android:id="@+id/layout_tip"
        android:layout_alignParentBottom="true"
        android:layout_marginBottom="30dp"
        android:gravity="center"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"/>

</RelativeLayout>