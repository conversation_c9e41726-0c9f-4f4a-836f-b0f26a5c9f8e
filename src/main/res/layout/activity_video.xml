<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:id="@+id/layout_root"
    android:background="@color/bar_grey"
    android:gravity="center"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center">
        <RelativeLayout
            android:id="@+id/videoParent"
            android:gravity="center"
            android:layout_gravity="center"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <com.leeson.image_pickers.views.FullScreenVideoView
                android:id="@+id/videoView"
                android:layout_width="1dp"
                android:layout_height="1dp"
                android:layout_centerInParent="true"/>
            <ImageView
                android:id="@+id/iv_src"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:visibility="gone"/>
            <ProgressBar
                android:id="@+id/progressBar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"/>
            <!--<include layout="@layout/title" />-->
        </RelativeLayout>

    </LinearLayout>

</LinearLayout>