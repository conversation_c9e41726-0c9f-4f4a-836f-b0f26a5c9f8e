package com.gankao.ai_sales_assist.flutter

import android.content.Context
import android.util.Log
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel
import com.gankao.ai_sales_assist.call.models.CallSummaryInfo
import com.gankao.ai_sales_assist.jpush.PushHelper
import com.gk.logcat.LogUtil
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch

/// Flutter 通话监听通道 - 使用后台引擎版本
class CallMonitorChannel private constructor(
    private val context: Context,
    private val flutterEngine: FlutterEngine
) {
    private val methodChannel: MethodChannel =
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL_NAME)

    // 缓存数据
    private var cachedTagList: Map<String, Any?>? = null
    private var cachedCustomerGroups: Map<String, Any?>? = null
    private var cachedFollowStatus: Map<String, Any?>? = null
    private val scope = MainScope()

    companion object {
        private const val CHANNEL_NAME = "flutter_call_monitor"
        private const val TAG = "CallMonitorChannel"
        
        @Volatile
        private var instance: CallMonitorChannel? = null
        
        /**
         * 初始化通话监控通道，使用已存在的后台Flutter引擎
         * 不依赖MainActivity，使用应用程序上下文
         */
        fun initialize(context: Context): CallMonitorChannel? {
            return try {
                // 使用应用程序上下文，避免依赖Activity
                val appContext = context.applicationContext
                val backgroundEngineManager = BackgroundFlutterEngineManager.getInstance()
                
                // 获取已存在的后台引擎，不重复初始化
                val backgroundEngine = backgroundEngineManager.getBackgroundEngine()
                
                if (backgroundEngine != null) {
                    instance = CallMonitorChannel(appContext, backgroundEngine)
                    LogUtil.d(TAG, "CallMonitorChannel初始化成功，使用已存在的后台引擎")
                    instance
                } else {
                    LogUtil.e(TAG, "后台Flutter引擎不可用，无法创建CallMonitorChannel")
                    null
                }
            } catch (e: Exception) {
                LogUtil.e(TAG, "CallMonitorChannel初始化异常: ${e.message}")
                null
            }
        }
        
        fun getInstanceOrNull(): CallMonitorChannel? = instance
        
        /**
         * 清理实例，通常在Flutter引擎销毁时调用
         */
        fun clearInstance() {
            synchronized(this) {
                instance?.cleanup()
                instance = null
                LogUtil.d(TAG, "CallMonitorChannel实例已清理")
            }
        }
    }
    
    init {
        setupMethodCallHandler()
        LogUtil.d(TAG, "CallMonitorChannel 初始化完成 - 使用后台引擎版本")
    }
    
    /// 设置方法调用处理器
    private fun setupMethodCallHandler() {
        methodChannel.setMethodCallHandler { call, result ->
            when (call.method) {
                "requestPermissions" -> {
                    // 处理权限请求（如果需要从 Flutter 端请求）
                    result.success(true)
                }
                "startListening" -> {
                    // 处理开始监听请求
                    result.success(true)
                }
                "stopListening" -> {
                    // 处理停止监听请求
                    result.success(true)
                }
                "sendAiAnalysisResult" -> {
                    // 处理AI分析结果
                    handleAiAnalysisResult(call.arguments)
                    result.success(true)
                }
                else -> {
                    result.notImplemented()
                }
            }
        }
    }
    
    /// 处理通话结束 - 新的简化流程
    /// 1. 将通话数据发送给Flutter
    /// 2. Flutter获取客户信息
    /// 3. 返回客户信息用于显示悬浮窗
    /// 4. Flutter异步处理录音上传和事件上报
    fun handleCallEnd(callSummaryInfo: CallSummaryInfo, callback: (Map<String, Any?>?) -> Unit) {
        try {
            LogUtil.d(TAG, "处理通话结束 - 号码: ${callSummaryInfo.phoneNumber}")
            
            // 检查Flutter引擎连接状态
            if (!isFlutterEngineConnected()) {
                LogUtil.w(TAG, "Flutter引擎未连接，无法处理通话结束事件")
                callback(null)
                return
            }
            
            // 构建通话摘要数据
            val callSummaryData = buildCallSummaryData(callSummaryInfo)
            
            // 发送给Flutter处理
            methodChannel.invokeMethod("handleCallEnd", callSummaryData, object : MethodChannel.Result {
                override fun success(result: Any?) {
                    LogUtil.d(TAG, "Flutter处理通话结束成功: $result")
                    if (result is Map<*, *>) {
                        @Suppress("UNCHECKED_CAST")
                        val resultMap = result as Map<String, Any?>
                        
                        // 直接传递完整的响应给回调
                        callback(resultMap)
                    } else {
                        LogUtil.e(TAG, "Flutter返回数据格式错误")
                        callback(null)
                    }
                }
                
                override fun error(errorCode: String, errorMessage: String?, errorDetails: Any?) {
                    LogUtil.e(TAG, "Flutter处理通话结束失败: $errorCode - $errorMessage")
                    callback(null)
                }

                override fun notImplemented() {
                    LogUtil.e(TAG, "Flutter处理通话结束方法未实现")
                    callback(null)
                }
            })

        } catch (e: Exception) {
            LogUtil.e(TAG, "处理通话结束异常: ${e.message}")
            // 如果是Flutter引擎断开连接的异常，记录特殊日志
            if (e.message?.contains("FlutterJNI") == true || e.message?.contains("detached") == true) {
                LogUtil.w(TAG, "检测到Flutter引擎断开连接，通话结束事件处理失败")
            }
            callback(null)
        }
    }
    
    /// 构建通话摘要数据
    private fun buildCallSummaryData(callSummaryInfo: CallSummaryInfo): Map<String, Any?> {
        val recordingsList = callSummaryInfo.recordings.map { recording ->
            mapOf(
                "filePath" to recording.path,
                "duration" to "0",
                "size" to recording.size.toInt()
            )
        }
        
        return mapOf(
            "phoneNumber" to callSummaryInfo.phoneNumber,
            "callType" to callSummaryInfo.callType,
            "duration" to callSummaryInfo.duration,
            "startTime" to callSummaryInfo.startTime,
            "endTime" to callSummaryInfo.endTime,
            "recordings" to recordingsList
        )
    }
    
    /// 发送通话状态变化到 Flutter
    fun notifyRefreshMessage() {
        try {
            // 检查Flutter引擎连接状态
            if (!isFlutterEngineConnected()) {
                LogUtil.w(TAG, "Flutter引擎未连接，无法发送刷新消息")
                return
            }
            
            scope.launch {
                methodChannel.invokeMethod("notify_refresh_message", "")
            }

        } catch (e: Exception) {
            Log.e(TAG, "notifyRefreshMessage Flutter 失败", e)
            if (e.message?.contains("FlutterJNI") == true || e.message?.contains("detached") == true) {
                LogUtil.w(TAG, "检测到Flutter引擎断开连接，刷新消息发送失败")
            }
        }
    }
    
    /// 请求获取标签列表
    fun requestTagList(callback: (Map<String, Any?>) -> Unit) {
        // 如果有缓存，直接返回
        cachedTagList?.let { cache ->
            Log.d(TAG, "使用缓存的标签列表")
            callback(cache)
            return
        }
        
        try {
            methodChannel.invokeMethod("getUserAllTags", null, object : MethodChannel.Result {
                override fun success(result: Any?) {
                    Log.d(TAG, "获取标签列表成功: $result")
                    if (result is Map<*, *>) {
                        @Suppress("UNCHECKED_CAST")
                        val resultMap = result as Map<String, Any?>
                        // 缓存数据
                        cachedTagList = resultMap
                        callback(resultMap)
                    } else {
                        Log.e(TAG, "标签列表数据格式错误")
                        callback(createErrorResponse("数据格式错误", emptyTagsStructure()))
                    }
                }
                
                override fun error(errorCode: String, errorMessage: String?, errorDetails: Any?) {
                    Log.e(TAG, "获取标签列表失败: $errorCode - $errorMessage")
                    callback(createErrorResponse(errorMessage ?: "获取标签列表失败", emptyTagsStructure()))
                }
                
                override fun notImplemented() {
                    Log.e(TAG, "获取标签列表方法未实现")
                    callback(createErrorResponse("方法未实现", emptyTagsStructure()))
                }
            })
        } catch (e: Exception) {
            Log.e(TAG, "请求标签列表异常", e)
            callback(createErrorResponse("请求异常: ${e.message}", emptyTagsStructure()))
        }
    }
    
    /// 请求获取客户分组列表
    fun requestCustomerGroups(callback: (Map<String, Any?>) -> Unit) {
        // 如果有缓存，直接返回
        cachedCustomerGroups?.let { cache ->
            Log.d(TAG, "使用缓存的客户分组列表")
            callback(cache)
            return
        }
        
        try {
            methodChannel.invokeMethod("getCustomerGroups", null, object : MethodChannel.Result {
                override fun success(result: Any?) {
                    Log.d(TAG, "获取客户分组列表成功: $result")
                    if (result is Map<*, *>) {
                        @Suppress("UNCHECKED_CAST")
                        val resultMap = result as Map<String, Any?>
                        // 缓存数据
                        cachedCustomerGroups = resultMap
                        callback(resultMap)
                    } else {
                        Log.e(TAG, "客户分组列表数据格式错误")
                        callback(createErrorResponse("数据格式错误", mapOf("customerGroups" to emptyList<Map<String, Any?>>())))
                    }
                }
                
                override fun error(errorCode: String, errorMessage: String?, errorDetails: Any?) {
                    Log.e(TAG, "获取客户分组列表失败: $errorCode - $errorMessage")
                    callback(createErrorResponse(errorMessage ?: "获取客户分组列表失败", mapOf("customerGroups" to emptyList<Map<String, Any?>>())))
                }
                
                override fun notImplemented() {
                    Log.e(TAG, "获取客户分组列表方法未实现")
                    callback(createErrorResponse("方法未实现", mapOf("customerGroups" to emptyList<Map<String, Any?>>())))
                }
            })
        } catch (e: Exception) {
            Log.e(TAG, "请求客户分组列表异常", e)
            callback(createErrorResponse("请求异常: ${e.message}", mapOf("customerGroups" to emptyList<Map<String, Any?>>())))
        }
    }
    
    /// 请求获取跟进状态列表
    fun requestFollowStatus(callback: (Map<String, Any?>) -> Unit) {
        // 如果有缓存，直接返回
        cachedFollowStatus?.let { cache ->
            Log.d(TAG, "使用缓存的跟进状态列表")
            callback(cache)
            return
        }
        
        try {
            methodChannel.invokeMethod("getFollowStatus", null, object : MethodChannel.Result {
                override fun success(result: Any?) {
                    Log.d(TAG, "获取跟进状态列表成功: $result")
                    if (result is Map<*, *>) {
                        @Suppress("UNCHECKED_CAST")
                        val resultMap = result as Map<String, Any?>
                        // 缓存数据
                        cachedFollowStatus = resultMap
                        callback(resultMap)
                    } else {
                        Log.e(TAG, "跟进状态列表数据格式错误")
                        callback(createErrorResponse("数据格式错误", mapOf("followStatus" to emptyList<Map<String, Any?>>())))
                    }
                }
                
                override fun error(errorCode: String, errorMessage: String?, errorDetails: Any?) {
                    Log.e(TAG, "获取跟进状态列表失败: $errorCode - $errorMessage")
                    callback(createErrorResponse(errorMessage ?: "获取跟进状态列表失败", mapOf("followStatus" to emptyList<Map<String, Any?>>())))
                }
                
                override fun notImplemented() {
                    Log.e(TAG, "获取跟进状态列表方法未实现")
                    callback(createErrorResponse("方法未实现", mapOf("followStatus" to emptyList<Map<String, Any?>>())))
                }
            })
        } catch (e: Exception) {
            Log.e(TAG, "请求跟进状态列表异常", e)
            callback(createErrorResponse("请求异常: ${e.message}", mapOf("followStatus" to emptyList<Map<String, Any?>>())))
        }
    }
    
    /// 清空所有缓存
    fun clearAllCache() {
        cachedTagList = null
        cachedCustomerGroups = null
        cachedFollowStatus = null
        Log.d(TAG, "所有缓存已清空")
    }
    
    /**
     * 检查Flutter引擎是否连接
     */
    private fun isFlutterEngineConnected(): Boolean {
        return BackgroundFlutterEngineManager.getInstance().isBackgroundEngineAvailable()
    }
    
    /**
     * 清理资源
     */
    private fun cleanup() {
        try {
            // 清理缓存
            clearAllCache()
            
            // 清理方法通道处理器
            methodChannel.setMethodCallHandler(null)
            
            LogUtil.d(TAG, "CallMonitorChannel资源清理完成")
        } catch (e: Exception) {
            LogUtil.e(TAG, "清理CallMonitorChannel资源时发生异常: ${e.message}")
        }
    }

    /// 通过手机号获取客户信息 (保留兼容性)
    fun requestCustomerInfoByMobile(mobile: String, callback: (Map<String, Any?>) -> Unit) {
        try {
            Log.d(TAG, "开始请求客户信息，手机号: $mobile")
            val requestData = mapOf("mobile" to mobile)
            
            methodChannel.invokeMethod("getCustomerInfoByMobile", requestData, object : MethodChannel.Result {
                override fun success(result: Any?) {
                    Log.d(TAG, "获取客户信息成功: $result")
                    if (result is Map<*, *>) {
                        @Suppress("UNCHECKED_CAST")
                        val resultMap = result as Map<String, Any?>
                        callback(resultMap)
                    } else {
                        Log.e(TAG, "客户信息数据格式错误")
                        callback(createErrorResponse("数据格式错误", mapOf("customerInfo" to null)))
                    }
                }
                
                override fun error(errorCode: String, errorMessage: String?, errorDetails: Any?) {
                    Log.e(TAG, "获取客户信息失败: $errorCode - $errorMessage")
                    callback(createErrorResponse(errorMessage ?: "获取客户信息失败", mapOf("customerInfo" to null)))
                }
                
                override fun notImplemented() {
                    Log.e(TAG, "获取客户信息方法未实现")
                    callback(createErrorResponse("方法未实现", mapOf("customerInfo" to null)))
                }
            })
        } catch (e: Exception) {
            Log.e(TAG, "请求客户信息异常", e)
            callback(createErrorResponse("请求异常: ${e.message}", mapOf("customerInfo" to null)))
        }
    }
    
    /// 创建错误响应
    private fun createErrorResponse(error: String, extraData: Map<String, Any?>): Map<String, Any?> {
        val baseResponse = mapOf(
            "success" to false,
            "error" to error
        )
        return baseResponse + extraData
    }
    
    /// 创建空标签结构
    private fun emptyTagsStructure(): Map<String, Any?> {
        return mapOf(
            "personalTags" to emptyList<Map<String, Any?>>(),
            "systemTags" to emptyList<Map<String, Any?>>(),
            "departmentTags" to emptyList<Map<String, Any?>>()
        )
    }
    
    /// 保存通话记录
    fun saveCallRecord(saveData: Map<String, Any?>, callback: (Boolean, String?) -> Unit) {
        try {
            Log.d(TAG, "保存通话记录: $saveData")
            
            methodChannel.invokeMethod("saveCallRecord", saveData, object : MethodChannel.Result {
                override fun success(result: Any?) {
                    Log.d(TAG, "保存通话记录成功: $result")
                    if (result is Map<*, *>) {
                        @Suppress("UNCHECKED_CAST")
                        val resultMap = result as Map<String, Any?>
                        val success = resultMap["success"] as? Boolean ?: false
                        val errorMessage = resultMap["error"] as? String
                        callback(success, errorMessage)
                    } else {
                        Log.e(TAG, "保存通话记录返回数据格式错误")
                        callback(false, "数据格式错误")
                    }
                }
                
                override fun error(errorCode: String, errorMessage: String?, errorDetails: Any?) {
                    Log.e(TAG, "保存通话记录失败: $errorCode - $errorMessage")
                    callback(false, errorMessage ?: "保存失败")
                }
                
                override fun notImplemented() {
                    Log.e(TAG, "保存通话记录方法未实现")
                    callback(false, "方法未实现")
                }
            })
        } catch (e: Exception) {
            Log.e(TAG, "保存通话记录异常", e)
            callback(false, "保存异常: ${e.message}")
        }
    }
    
    /// 新增客户跟进记录
    fun addFollowRecord(customerId: Int, content: String, callback: (Boolean, String?) -> Unit) {
        try {
            Log.d(TAG, "新增跟进记录: customerId=$customerId, content=$content")
            
            val data = mapOf(
                "customerId" to customerId,
                "content" to content
            )
            
            methodChannel.invokeMethod("addFollowRecord", data, object : MethodChannel.Result {
                override fun success(result: Any?) {
                    Log.d(TAG, "新增跟进记录成功: $result")
                    if (result is Map<*, *>) {
                        @Suppress("UNCHECKED_CAST")
                        val resultMap = result as Map<String, Any?>
                        val success = resultMap["success"] as? Boolean ?: false
                        val errorMessage = resultMap["error"] as? String
                        callback(success, errorMessage)
                    } else {
                        Log.e(TAG, "新增跟进记录返回结果格式错误: $result")
                        callback(false, "返回结果格式错误")
                    }
                }

                override fun error(errorCode: String, errorMessage: String?, errorDetails: Any?) {
                    Log.e(TAG, "新增跟进记录失败: $errorCode - $errorMessage")
                    callback(false, errorMessage ?: "新增跟进记录失败")
                }

                override fun notImplemented() {
                    Log.e(TAG, "新增跟进记录方法未实现")
                    callback(false, "方法未实现")
                }
            })
        } catch (e: Exception) {
            Log.e(TAG, "新增跟进记录异常", e)
            callback(false, "新增跟进记录异常: ${e.message}")
        }
    }
    
    /// 从公海认领客户
    fun claimCustomerFromPublicPool(customerIds: List<Int>, callback: (Boolean, String?) -> Unit) {
        try {
            Log.d(TAG, "从公海认领客户: customerIds=$customerIds")
            
            val data = mapOf(
                "customerIds" to customerIds
            )
            
            methodChannel.invokeMethod("claimCustomerFromPublicPool", data, object : MethodChannel.Result {
                override fun success(result: Any?) {
                    Log.d(TAG, "认领客户成功: $result")
                    if (result is Map<*, *>) {
                        @Suppress("UNCHECKED_CAST")
                        val resultMap = result as Map<String, Any?>
                        val success = resultMap["success"] as? Boolean ?: false
                        val errorMessage = resultMap["error"] as? String
                        callback(success, errorMessage)
                    } else {
                        Log.e(TAG, "认领客户返回结果格式错误: $result")
                        callback(false, "返回结果格式错误")
                    }
                }

                override fun error(errorCode: String, errorMessage: String?, errorDetails: Any?) {
                    Log.e(TAG, "认领客户失败: $errorCode - $errorMessage")
                    callback(false, errorMessage ?: "认领客户失败")
                }

                override fun notImplemented() {
                    Log.e(TAG, "认领客户方法未实现")
                    callback(false, "方法未实现")
                }
            })
        } catch (e: Exception) {
            Log.e(TAG, "认领客户异常", e)
            callback(false, "认领客户异常: ${e.message}")
        }
    }
    
    /// 创建新客户
    fun createCallCustomer(mobile: String, name: String, callback: (Boolean, Int?, String?) -> Unit) {
        try {
            Log.d(TAG, "创建新客户: mobile=$mobile, name=$name")
            
            val data = mapOf(
                "mobile" to mobile,
                "name" to name
            )
            
            methodChannel.invokeMethod("createCallCustomer", data, object : MethodChannel.Result {
                override fun success(result: Any?) {
                    Log.d(TAG, "创建客户成功: $result")
                    if (result is Map<*, *>) {
                        @Suppress("UNCHECKED_CAST")
                        val resultMap = result as Map<String, Any?>
                        val success = resultMap["success"] as? Boolean ?: false
                        val errorMessage = resultMap["error"] as? String
                        
                        if (success) {
                            val customerId = when (val id = resultMap["customerId"]) {
                                is Int -> id
                                is String -> id.toIntOrNull()
                                else -> null
                            }
                            callback(true, customerId, null)
                        } else {
                            callback(false, null, errorMessage)
                        }
                    } else {
                        Log.e(TAG, "创建客户返回结果格式错误: $result")
                        callback(false, null, "返回结果格式错误")
                    }
                }

                override fun error(errorCode: String, errorMessage: String?, errorDetails: Any?) {
                    Log.e(TAG, "创建客户失败: $errorCode - $errorMessage")
                    callback(false, null, errorMessage ?: "创建客户失败")
                }

                override fun notImplemented() {
                    Log.e(TAG, "创建客户方法未实现")
                    callback(false, null, "方法未实现")
                }
            })
        } catch (e: Exception) {
            Log.e(TAG, "创建客户异常", e)
            callback(false, null, "创建客户异常: ${e.message}")
        }
    }
    
    /// 处理AI分析结果
    private fun handleAiAnalysisResult(arguments: Any?) {
        try {
            Log.d(TAG, "收到AI分析结果: $arguments")

            if (arguments is Map<*, *>) {
                @Suppress("UNCHECKED_CAST")
                val analysisData = arguments as Map<String, Any?>

                // 通知悬浮窗更新AI分析结果
//                com.gankao.ai_sales_assist.call.ui.CallRecordModalWindow.updateAiAnalysisResult(analysisData)

            } else {
                Log.e(TAG, "AI分析结果数据格式错误")
            }

        } catch (e: Exception) {
            Log.e(TAG, "处理AI分析结果异常", e)
        }
    }
}