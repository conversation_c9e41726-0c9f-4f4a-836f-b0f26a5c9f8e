package com.gankao.ai_sales_assist
import android.app.Application
import android.content.Intent
import com.gankao.ai_sales_assist.flutterboost.FlutterMessageManager
import com.gankao.ai_sales_assist.jpush.PushHelper
import com.gankao.ai_sales_assist.utils.ToasterUtil
import com.gankao.ai_sales_assist.flutter.CallMonitorChannel
import com.gankao.ai_sales_assist.flutter.BackgroundUserManagerChannel
import com.gankao.ai_sales_assist.flutter.BackgroundFlutterEngineManager

import com.gankao.network.http.HttpGlobalConfig
import com.gk.logcat.LogUtil
import com.idlefish.flutterboost.FlutterBoost
import com.idlefish.flutterboost.FlutterBoostDelegate
import com.idlefish.flutterboost.FlutterBoostRouteOptions
import com.idlefish.flutterboost.containers.FlutterBoostActivity
//import com.tamsiree.rxkit.RxTool
import io.flutter.embedding.android.FlutterActivityLaunchConfigs
import io.flutter.embedding.engine.FlutterEngine

class MyApp : Application() {
    companion object {
        lateinit var app: MyApp
    }
    override fun onCreate() {
        super.onCreate()
        app = this

        // 初始化Toaster - 支持后台显示的Toast
        ToasterUtil.init(this)
        

        // 1. 首先初始化后台Flutter引擎管理器
        val backgroundEngineManager = BackgroundFlutterEngineManager.getInstance()
        val backgroundEngine = backgroundEngineManager.initializeBackgroundEngine(this)
        LogUtil.d("MyApp", "BackgroundFlutterEngineManager 已初始化")

        // 2. 然后初始化各个后台通道
        if (backgroundEngine != null) {
            // 初始化通话监控通道
            CallMonitorChannel.initialize(this)
            LogUtil.d("MyApp", "CallMonitorChannel 已初始化")
            
            // 初始化后台用户管理通道
            BackgroundUserManagerChannel.initialize(this)
            LogUtil.d("MyApp", "BackgroundUserManagerChannel 已初始化")
        } else {
            LogUtil.e("MyApp", "后台Flutter引擎初始化失败，无法初始化后台通道")
        }

        FlutterMessageManager.init()
        //极光推送
        PushHelper.init(this)

//        RxTool.init(this)
        LogUtil.init(this, cacheDir.absolutePath)
        LogUtil.maxCacheLog = 100

        HttpGlobalConfig.apply {
            isNeedUserRetry = false
            isNeedLoginGlobal = false
            timeout = 30000L
            retryCount = 3
        }
    }
    

}