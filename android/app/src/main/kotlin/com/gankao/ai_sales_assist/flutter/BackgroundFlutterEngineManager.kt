package com.gankao.ai_sales_assist.flutter

import android.content.Context
import android.util.Log
import com.gankao.ai_sales_assist.float_ball.FloatingBallPlugin
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.embedding.engine.dart.DartExecutor
import io.flutter.embedding.engine.loader.FlutterLoader
import com.gk.logcat.LogUtil
import com.gankao.ai_sales_assist.plugins.FollowUpReminderPlugin
import com.gankao.ai_sales_assist.plugins.BackgroundUserManagerPlugin

/**
 * 后台Flutter引擎管理器
 * 用于管理独立于UI的Flutter引擎，主要用于后台服务
 * 如通话监控、消息推送等不依赖UI的功能
 */
class BackgroundFlutterEngineManager private constructor() {
    
    companion object {
        private const val TAG = "BackgroundFlutterEngineManager"
        private const val BACKGROUND_ENGINE_ID = "background_flutter_engine"
        
        @Volatile
        private var instance: BackgroundFlutterEngineManager? = null
        
        fun getInstance(): BackgroundFlutterEngineManager {
            return instance ?: synchronized(this) {
                instance ?: BackgroundFlutterEngineManager().also { instance = it }
            }
        }
    }
    
    private var backgroundEngine: FlutterEngine? = null
    private var isEngineInitialized = false
    
    /**
     * 初始化后台Flutter引擎
     */
    fun initializeBackgroundEngine(context: Context): FlutterEngine? {
        try {
            if (backgroundEngine != null && isEngineInitialized) {
                LogUtil.d(TAG, "后台Flutter引擎已存在，无需重复初始化")
                return backgroundEngine
            }
            
            LogUtil.d(TAG, "开始初始化后台Flutter引擎")
            
            // 确保Flutter已初始化
            val flutterLoader = FlutterLoader()
            flutterLoader.startInitialization(context.applicationContext)
            flutterLoader.ensureInitializationComplete(context.applicationContext, null)
            
            // 创建独立的Flutter引擎
            backgroundEngine = FlutterEngine(context.applicationContext)
            
            // 启动Dart执行器
            val dartEntrypoint = DartExecutor.DartEntrypoint.createDefault()
            backgroundEngine?.dartExecutor?.executeDartEntrypoint(dartEntrypoint)

            // 注册插件
//            backgroundEngine?.plugins?.add(FloatingBallPlugin())
//            backgroundEngine?.plugins?.add(FollowUpReminderPlugin())
            // 使用Channel方式，不注册BackgroundUserManagerPlugin
            // backgroundEngine?.plugins?.add(BackgroundUserManagerPlugin())
            LogUtil.d(TAG, "后台引擎插件注册完成")
            
            isEngineInitialized = true
            LogUtil.d(TAG, "后台Flutter引擎初始化成功")
            
            return backgroundEngine
            
        } catch (e: Exception) {
            LogUtil.e(TAG, "后台Flutter引擎初始化失败: ${e.message}")
            backgroundEngine = null
            isEngineInitialized = false
            return null
        }
    }
    
    /**
     * 获取后台Flutter引擎
     */
    fun getBackgroundEngine(): FlutterEngine? {
        return if (isEngineInitialized && backgroundEngine != null) {
            backgroundEngine
        } else {
            LogUtil.w(TAG, "后台Flutter引擎未初始化或已销毁")
            null
        }
    }
    
    /**
     * 检查后台引擎是否可用
     */
    fun isBackgroundEngineAvailable(): Boolean {
        return try {
            backgroundEngine?.dartExecutor?.isExecutingDart == true
        } catch (e: Exception) {
            LogUtil.w(TAG, "检查后台引擎状态失败: ${e.message}")
            false
        }
    }
    
    /**
     * 重启后台Flutter引擎
     */
    fun restartBackgroundEngine(context: Context): FlutterEngine? {
        LogUtil.d(TAG, "重启后台Flutter引擎")
        destroyBackgroundEngine()
        return initializeBackgroundEngine(context)
    }
    
    /**
     * 销毁后台Flutter引擎
     */
    fun destroyBackgroundEngine() {
        try {
            LogUtil.d(TAG, "销毁后台Flutter引擎")
            
            backgroundEngine?.destroy()
            backgroundEngine = null
            isEngineInitialized = false
            
            LogUtil.d(TAG, "后台Flutter引擎已销毁")
            
        } catch (e: Exception) {
            LogUtil.e(TAG, "销毁后台Flutter引擎失败: ${e.message}")
        }
    }
    
    /**
     * 获取引擎状态信息
     */
    fun getEngineStatus(): Map<String, Any> {
        return mapOf(
            "isInitialized" to isEngineInitialized,
            "isEngineNull" to (backgroundEngine == null),
            "isDartExecuting" to (backgroundEngine?.dartExecutor?.isExecutingDart ?: false),
            "engineId" to BACKGROUND_ENGINE_ID
        )
    }
}