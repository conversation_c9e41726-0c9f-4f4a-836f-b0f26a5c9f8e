package com.gankao.ai_sales_assist.flutter

import android.content.Context
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel
import com.gankao.ai_sales_assist.jpush.PushHelper
import com.gankao.ai_sales_assist.utils.init
import com.gankao.base.util.GsonUtils
import com.gk.logcat.LogUtil
import com.gk.logcat_upload.LogcatManager
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch

/**
 * 后台用户管理通道 - 使用后台引擎版本
 * 处理用户相关的后台操作，如登录状态管理、日志记录等
 * 不依赖MainActivity，独立运行
 */
class BackgroundUserManagerChannel private constructor(
    private val context: Context,
    private val flutterEngine: FlutterEngine
) {
    private val methodChannel: MethodChannel =
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL_NAME)
    
    private val scope = MainScope()

    companion object {
        private const val CHANNEL_NAME = "background_user_manager"
        private const val TAG = "BackgroundUserManagerChannel"
        
        @Volatile
        private var instance: BackgroundUserManagerChannel? = null
        
        /**
         * 初始化后台用户管理通道，使用后台Flutter引擎
         * 不依赖MainActivity，使用应用程序上下文
         */
        fun initialize(context: Context): BackgroundUserManagerChannel? {
            return try {
                // 使用应用程序上下文，避免依赖Activity
                val appContext = context.applicationContext
                val backgroundEngineManager = BackgroundFlutterEngineManager.getInstance()
                val backgroundEngine = backgroundEngineManager.initializeBackgroundEngine(appContext)
                
                if (backgroundEngine != null) {
                    instance = BackgroundUserManagerChannel(appContext, backgroundEngine)
                    LogUtil.d(TAG, "BackgroundUserManagerChannel初始化成功，使用后台引擎，不依赖MainActivity")
                    instance
                } else {
                    LogUtil.e(TAG, "后台Flutter引擎初始化失败，无法创建BackgroundUserManagerChannel")
                    null
                }
            } catch (e: Exception) {
                LogUtil.e(TAG, "BackgroundUserManagerChannel初始化异常: ${e.message}")
                null
            }
        }
        
        fun getInstanceOrNull(): BackgroundUserManagerChannel? = instance
        
        /**
         * 清理实例，通常在Flutter引擎销毁时调用
         */
        fun clearInstance() {
            synchronized(this) {
                instance?.cleanup()
                instance = null
                LogUtil.d(TAG, "BackgroundUserManagerChannel实例已清理")
            }
        }
    }
    
    init {
        setupMethodCallHandler()
        LogUtil.d(TAG, "BackgroundUserManagerChannel 初始化完成 - 使用后台引擎版本")
    }
    
    /**
     * 设置方法调用处理器
     */
    private fun setupMethodCallHandler() {
        methodChannel.setMethodCallHandler { call, result ->
            when (call.method) {
                "updateUser" -> {
                    handleUpdateUser(call.arguments, result)
                }
                "logoutUser" -> {
                    handleLogoutUser(result)
                }
                "nativeLog" -> {
                    handleNativeLog(call.arguments, result)
                }
                "initLogcat" -> {
                    handleInitLogcat(result)
                }
                else -> {
                    LogUtil.w(TAG, "未知方法调用: ${call.method}")
                    result.notImplemented()
                }
            }
        }
    }
    
    /**
     * 处理用户更新
     */
    private fun handleUpdateUser(arguments: Any?, result: MethodChannel.Result) {
        try {
            if (!isFlutterEngineConnected()) {
                LogUtil.w(TAG, "Flutter引擎未连接，无法处理updateUser")
                result.error("ENGINE_NOT_CONNECTED", "Flutter引擎未连接", null)
                return
            }
            
            val args = arguments as? Map<String, Any?>
            val userId = args?.get("userId") as? String
            val env = args?.get("appEnv") as? String
            
            if (userId == null) {
                LogUtil.e(TAG, "updateUser: userId 不能为空")
                result.error("INVALID_ARGUMENT", "userId 不能为空", null)
                return
            }
            
            val end = when (env) {
                "debug" -> "_dev"
                "preview" -> "_test"
                else -> ""
            }
            
            val finalUserId = userId + end
            
            LogUtil.d(TAG, "updateUser: $finalUserId")
            
            scope.launch {
                try {
                    // 设置极光推送别名
                    PushHelper.setAlias(context, finalUserId)
                    
                    // 初始化日志系统
                    LogcatManager.init(context, finalUserId)
                    
                    LogUtil.i(TAG, "用户更新完成: $finalUserId")
                    result.success(true)
                } catch (e: Exception) {
                    LogUtil.e(TAG, "用户更新失败: ${e.message}")
                    result.error("UPDATE_FAILED", "用户更新失败: ${e.message}", null)
                }
            }
            
        } catch (e: Exception) {
            LogUtil.e(TAG, "handleUpdateUser异常: ${e.message}")
            result.error("EXCEPTION", "处理用户更新时发生异常: ${e.message}", null)
        }
    }
    
    /**
     * 处理用户登出
     */
    private fun handleLogoutUser(result: MethodChannel.Result) {
        try {
            if (!isFlutterEngineConnected()) {
                LogUtil.w(TAG, "Flutter引擎未连接，无法处理logoutUser")
                result.error("ENGINE_NOT_CONNECTED", "Flutter引擎未连接", null)
                return
            }
            
            scope.launch {
                try {
                    // 清除极光推送别名
                    PushHelper.deleteAlias(context)
                    
                    // 清理日志系统
                    LogcatManager.clear()
                    
                    LogUtil.d(TAG, "用户登出完成")
                    result.success(true)
                } catch (e: Exception) {
                    LogUtil.e(TAG, "用户登出失败: ${e.message}")
                    result.error("LOGOUT_FAILED", "用户登出失败: ${e.message}", null)
                }
            }
            
        } catch (e: Exception) {
            LogUtil.e(TAG, "handleLogoutUser异常: ${e.message}")
            result.error("EXCEPTION", "处理用户登出时发生异常: ${e.message}", null)
        }
    }
    
    /**
     * 处理原生日志记录
     */
    private fun handleNativeLog(arguments: Any?, result: MethodChannel.Result) {
        try {
            if (!isFlutterEngineConnected()) {
                LogUtil.w(TAG, "Flutter引擎未连接，无法处理nativeLog")
                result.error("ENGINE_NOT_CONNECTED", "Flutter引擎未连接", null)
                return
            }
            
            val args = arguments as? Map<String, Any?>
            val level = args?.get("level") as? String ?: "d"
            val tag = args?.get("tag") as? String ?: "Flutter"
            val message = args?.get("message") as? String ?: ""
            val data = args?.get("data")
            
            val logMessage = if (data != null) {
                "$message, data: ${GsonUtils.toJson(data)}"
            } else {
                message
            }
            
            when (level.lowercase()) {
                "v" -> LogUtil.v(tag, logMessage)
                "d" -> LogUtil.d(tag, logMessage)
                "i" -> LogUtil.i(tag, logMessage)
                "w" -> LogUtil.w(tag, logMessage)
                "e" -> LogUtil.e(tag, logMessage)
                else -> LogUtil.d(tag, logMessage)
            }
            
            result.success(true)
            
        } catch (e: Exception) {
            LogUtil.e(TAG, "handleNativeLog异常: ${e.message}")
            result.error("EXCEPTION", "处理原生日志时发生异常: ${e.message}", null)
        }
    }
    
    /**
     * 处理日志系统初始化
     */
    private fun handleInitLogcat(result: MethodChannel.Result) {
        try {
            if (!isFlutterEngineConnected()) {
                LogUtil.w(TAG, "Flutter引擎未连接，无法处理initLogcat")
                result.error("ENGINE_NOT_CONNECTED", "Flutter引擎未连接", null)
                return
            }
            
            scope.launch {
                try {
                    context.init()
                    LogUtil.d(TAG, "日志系统初始化完成")
                    result.success(true)
                } catch (e: Exception) {
                    LogUtil.e(TAG, "日志系统初始化失败: ${e.message}")
                    result.error("INIT_FAILED", "日志系统初始化失败: ${e.message}", null)
                }
            }
            
        } catch (e: Exception) {
            LogUtil.e(TAG, "handleInitLogcat异常: ${e.message}")
            result.error("EXCEPTION", "处理日志系统初始化时发生异常: ${e.message}", null)
        }
    }
    
    /**
     * 检查Flutter引擎连接状态
     */
    private fun isFlutterEngineConnected(): Boolean {
        return try {
            flutterEngine.dartExecutor.isExecutingDart
        } catch (e: Exception) {
            LogUtil.w(TAG, "检查Flutter引擎连接状态失败: ${e.message}")
            false
        }
    }
    
    /**
     * 清理资源
     */
    private fun cleanup() {
        try {
            methodChannel.setMethodCallHandler(null)
            LogUtil.d(TAG, "BackgroundUserManagerChannel资源清理完成")
        } catch (e: Exception) {
            LogUtil.e(TAG, "清理BackgroundUserManagerChannel资源时发生异常: ${e.message}")
        }
    }
    
    /**
     * 通知用户状态变更
     */
    fun notifyUserStatusChanged(userId: String?, isLoggedIn: Boolean) {
        try {
            if (!isFlutterEngineConnected()) {
                LogUtil.w(TAG, "Flutter引擎未连接，无法发送用户状态变更通知")
                return
            }
            
            val data = mapOf(
                "userId" to userId,
                "isLoggedIn" to isLoggedIn,
                "timestamp" to System.currentTimeMillis()
            )
            
            scope.launch {
                try {
                    methodChannel.invokeMethod("onUserStatusChanged", data)
                    LogUtil.d(TAG, "用户状态变更通知已发送: userId=$userId, isLoggedIn=$isLoggedIn")
                } catch (e: Exception) {
                    LogUtil.e(TAG, "发送用户状态变更通知失败: ${e.message}")
                }
            }
            
        } catch (e: Exception) {
            LogUtil.e(TAG, "notifyUserStatusChanged异常: ${e.message}")
        }
    }
    
    /**
     * 获取通道状态信息
     */
    fun getChannelStatus(): Map<String, Any> {
        return mapOf(
            "channelName" to CHANNEL_NAME,
            "isEngineConnected" to isFlutterEngineConnected(),
            "instanceCreated" to (instance != null),
            "contextAvailable" to (context != null)
        )
    }
}