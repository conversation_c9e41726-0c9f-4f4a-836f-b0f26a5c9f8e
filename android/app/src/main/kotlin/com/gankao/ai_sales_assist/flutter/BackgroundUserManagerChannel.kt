package com.gankao.ai_sales_assist.flutter

import android.content.Context
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel
import com.gankao.ai_sales_assist.jpush.PushHelper
import com.gankao.ai_sales_assist.utils.init
import com.gankao.base.util.GsonUtils
import com.gk.logcat.LogUtil
import com.gk.logcat_upload.LogcatManager
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import com.gankao.ai_sales_assist.flutter.BackgroundFlutterEngineManager

/**
 * 后台用户管理通道 - 使用后台引擎版本
 * 处理用户相关的后台操作，如登录状态管理、日志记录等
 * 不依赖MainActivity，独立运行
 */
class BackgroundUserManagerChannel private constructor(
    private val context: Context,
    private val flutterEngine: FlutterEngine
) {
    private val methodChannel: MethodChannel =
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL_NAME)
    
    private val scope = MainScope()

    companion object {
        private const val CHANNEL_NAME = "background_user_manager"
        private const val TAG = "BackgroundUserManagerChannel"
        
        @Volatile
        private var instance: BackgroundUserManagerChannel? = null
        
        /**
         * 初始化后台用户管理通道，使用已存在的后台Flutter引擎
         * 不依赖MainActivity，使用应用程序上下文
         */
        fun initialize(context: Context): BackgroundUserManagerChannel? {
            return try {
                // 使用应用程序上下文，避免依赖Activity
                val appContext = context.applicationContext
                val backgroundEngineManager = BackgroundFlutterEngineManager.getInstance()

                // 获取已存在的后台引擎，不重复初始化
                val backgroundEngine = backgroundEngineManager.getBackgroundEngine()

                if (backgroundEngine != null) {
                    // 检查引擎是否真正可用
                    if (backgroundEngine.dartExecutor.isExecutingDart) {
                        instance = BackgroundUserManagerChannel(appContext, backgroundEngine)
                        LogUtil.d(TAG, "BackgroundUserManagerChannel初始化成功，使用已存在的后台引擎")
                        instance
                    } else {
                        LogUtil.w(TAG, "后台Flutter引擎未执行Dart代码，等待引擎启动...")
                        // 等待引擎启动
                        android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                            if (backgroundEngine.dartExecutor.isExecutingDart) {
                                instance = BackgroundUserManagerChannel(appContext, backgroundEngine)
                                LogUtil.d(TAG, "BackgroundUserManagerChannel延迟初始化成功")
                            } else {
                                LogUtil.e(TAG, "后台Flutter引擎启动超时")
                            }
                        }, 2000)
                        null
                    }
                } else {
                    LogUtil.e(TAG, "后台Flutter引擎不可用，无法创建BackgroundUserManagerChannel")
                    null
                }
            } catch (e: Exception) {
                LogUtil.e(TAG, "BackgroundUserManagerChannel初始化异常: ${e.message}")
                null
            }
        }
        
        fun getInstanceOrNull(): BackgroundUserManagerChannel? = instance
        
        /**
         * 清理实例，通常在Flutter引擎销毁时调用
         */
        fun clearInstance() {
            synchronized(this) {
                instance?.cleanup()
                instance = null
                LogUtil.d(TAG, "BackgroundUserManagerChannel实例已清理")
            }
        }
    }
    
    init {
        setupMethodCallHandler()
        LogUtil.d(TAG, "BackgroundUserManagerChannel 初始化完成 - 使用后台引擎版本")
    }
    
    /**
     * 设置方法调用处理器
     */
    private fun setupMethodCallHandler() {
        methodChannel.setMethodCallHandler { call, result ->
            when (call.method) {
                "updateUser" -> {
                    handleUpdateUser(call.arguments, result)
                }
                "logoutUser" -> {
                    handleLogoutUser(result)
                }
                "nativeLog" -> {
                    handleNativeLog(call.arguments, result)
                }
                "initLogcat" -> {
                    handleInitLogcat(result)
                }
                else -> {
                    LogUtil.w(TAG, "未知方法调用: ${call.method}")
                    result.notImplemented()
                }
            }
        }
    }
    
    /**
     * 处理用户更新
     */
    private fun handleUpdateUser(arguments: Any?, result: MethodChannel.Result) {
        try {
            if (!isFlutterEngineConnected()) {
                LogUtil.w(TAG, "Flutter引擎未连接，无法处理updateUser")
                result.error("ENGINE_NOT_CONNECTED", "Flutter引擎未连接", null)
                return
            }
            
            val args = arguments as? Map<String, Any?>
            val userId = args?.get("userId") as? String
            val env = args?.get("appEnv") as? String
            
            if (userId == null) {
                LogUtil.e(TAG, "updateUser: userId 不能为空")
                result.error("INVALID_ARGUMENT", "userId 不能为空", null)
                return
            }
            
            val end = when (env) {
                "debug" -> "_dev"
                "preview" -> "_test"
                else -> ""
            }
            
            val finalUserId = userId + end
            
            LogUtil.d(TAG, "updateUser: $finalUserId")
            
            scope.launch {
                try {
                    // 设置极光推送别名
                    PushHelper.setAlias(finalUserId)
                    
                    // 初始化日志系统
                    LogcatManager.init(finalUserId)
                    
                    LogUtil.i(TAG, "用户更新完成: $finalUserId")
                    result.success(true)
                } catch (e: Exception) {
                    LogUtil.e(TAG, "用户更新失败: ${e.message}")
                    result.error("UPDATE_FAILED", "用户更新失败: ${e.message}", null)
                }
            }
            
        } catch (e: Exception) {
            LogUtil.e(TAG, "handleUpdateUser异常: ${e.message}")
            result.error("EXCEPTION", "处理用户更新时发生异常: ${e.message}", null)
        }
    }
    
    /**
     * 处理用户登出
     */
    private fun handleLogoutUser(result: MethodChannel.Result) {
        try {
            if (!isFlutterEngineConnected()) {
                LogUtil.w(TAG, "Flutter引擎未连接，无法处理logoutUser")
                result.error("ENGINE_NOT_CONNECTED", "Flutter引擎未连接", null)
                return
            }
            
            LogUtil.d(TAG, "logoutUser: 开始处理用户登出")
            
            scope.launch {
                try {
                    // 清除极光推送别名
                    PushHelper.clearAlias()
                    
                    // 注意：LogcatManager没有clear方法，日志清理由系统自动管理
                    
                    LogUtil.i(TAG, "用户登出成功")
                    result.success(true)
                } catch (e: Exception) {
                    LogUtil.e(TAG, "用户登出失败: ${e.message}")
                    result.error("LOGOUT_FAILED", "用户登出失败: ${e.message}", null)
                }
            }
            
        } catch (e: Exception) {
            LogUtil.e(TAG, "handleLogoutUser异常: ${e.message}")
            result.error("EXCEPTION", "处理用户登出时发生异常: ${e.message}", null)
        }
    }
    
    /**
     * 处理原生日志记录
     */
    private fun handleNativeLog(arguments: Any?, result: MethodChannel.Result) {
        try {
            if (!isFlutterEngineConnected()) {
                LogUtil.w(TAG, "Flutter引擎未连接，尝试重新连接")
                
                // 尝试重新获取引擎并重新设置Channel
                if (tryReconnectEngine()) {
                    LogUtil.d(TAG, "引擎重连成功，继续处理nativeLog")
                } else {
                    LogUtil.e(TAG, "引擎重连失败，无法处理nativeLog")
                    result.error("ENGINE_NOT_CONNECTED", "Flutter引擎未连接且重连失败", null)
                    return
                }
            }
            
            LogUtil.d(TAG, "收到nativeLog调用: $arguments")
            
            // 将参数转换为JSON字符串并记录
            val logData = GsonUtils.toJson(arguments)
            LogUtil.i(logData)
            
            result.success(true)
            
        } catch (e: Exception) {
            LogUtil.e(TAG, "handleNativeLog异常: ${e.message}")
            result.error("NATIVE_LOG_ERROR", e.message, null)
        }
    }
    
    /**
     * 处理日志系统初始化
     */
    private fun handleInitLogcat(result: MethodChannel.Result) {
        try {
            if (!isFlutterEngineConnected()) {
                LogUtil.w(TAG, "Flutter引擎未连接，尝试重新连接")
                
                // 尝试重新获取引擎并重新设置Channel
                if (tryReconnectEngine()) {
                    LogUtil.d(TAG, "引擎重连成功，继续处理initLogcat")
                } else {
                    LogUtil.e(TAG, "引擎重连失败，无法处理initLogcat")
                    result.error("ENGINE_NOT_CONNECTED", "Flutter引擎未连接且重连失败", null)
                    return
                }
            }
            
            LogUtil.d(TAG, "收到initLogcat调用")
            
            // 这里可以添加额外的日志系统初始化逻辑
            // 目前LogcatManager的初始化在updateUser中处理
            
            result.success(true)
            
        } catch (e: Exception) {
            LogUtil.e(TAG, "handleInitLogcat异常: ${e.message}")
            result.error("INIT_LOGCAT_ERROR", e.message, null)
        }
    }
    
    /**
     * 检查Flutter引擎是否连接
     */
    private fun isFlutterEngineConnected(): Boolean {
        return try {
            flutterEngine.dartExecutor.isExecutingDart
        } catch (e: Exception) {
            LogUtil.e(TAG, "检查Flutter引擎连接状态异常: ${e.message}")
            false
        }
    }
    
    /**
     * 尝试重新连接Flutter引擎
     */
    private fun tryReconnectEngine(): Boolean {
        return try {
            LogUtil.d(TAG, "尝试重新连接Flutter引擎")
            
            // 从BackgroundFlutterEngineManager获取当前引擎状态
            val engineManager = BackgroundFlutterEngineManager.getInstance()
            val currentEngine = engineManager.getBackgroundEngine()
            
            if (currentEngine != null && currentEngine.dartExecutor.isExecutingDart) {
                LogUtil.d(TAG, "发现可用的后台引擎，重新设置MethodCallHandler")
                
                // 重新设置MethodCallHandler
                setupMethodCallHandler()
                
                LogUtil.d(TAG, "引擎重连成功")
                true
            } else {
                LogUtil.w(TAG, "后台引擎不可用，尝试重启引擎")
                
                // 尝试重启后台引擎
                val restartedEngine = engineManager.restartBackgroundEngine(context)
                if (restartedEngine != null) {
                    LogUtil.d(TAG, "后台引擎重启成功，重新设置MethodCallHandler")
                    setupMethodCallHandler()
                    true
                } else {
                    LogUtil.e(TAG, "后台引擎重启失败")
                    false
                }
            }
        } catch (e: Exception) {
            LogUtil.e(TAG, "重连Flutter引擎异常: ${e.message}")
            false
        }
    }
    
    /**
     * 清理资源
     */
    private fun cleanup() {
        try {
            methodChannel.setMethodCallHandler(null)
            LogUtil.d(TAG, "BackgroundUserManagerChannel资源清理完成")
        } catch (e: Exception) {
            LogUtil.e(TAG, "清理BackgroundUserManagerChannel资源时发生异常: ${e.message}")
        }
    }
    
    /**
     * 通知用户状态变更
     */
    fun notifyUserStatusChanged(userId: String?, isLoggedIn: Boolean) {
        try {
            if (!isFlutterEngineConnected()) {
                LogUtil.w(TAG, "Flutter引擎未连接，无法发送用户状态变更通知")
                return
            }
            
            val data = mapOf(
                "userId" to userId,
                "isLoggedIn" to isLoggedIn,
                "timestamp" to System.currentTimeMillis()
            )
            
            scope.launch {
                try {
                    methodChannel.invokeMethod("onUserStatusChanged", data)
                    LogUtil.d(TAG, "用户状态变更通知已发送: userId=$userId, isLoggedIn=$isLoggedIn")
                } catch (e: Exception) {
                    LogUtil.e(TAG, "发送用户状态变更通知失败: ${e.message}")
                }
            }
            
        } catch (e: Exception) {
            LogUtil.e(TAG, "notifyUserStatusChanged异常: ${e.message}")
        }
    }
    
    /**
     * 获取通道状态信息
     */
    fun getChannelStatus(): Map<String, Any> {
        return mapOf(
            "channelName" to CHANNEL_NAME,
            "isEngineConnected" to isFlutterEngineConnected(),
            "instanceCreated" to (instance != null),
            "contextAvailable" to (context != null)
        )
    }
}