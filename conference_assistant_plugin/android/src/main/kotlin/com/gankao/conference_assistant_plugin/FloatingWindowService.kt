package com.gankao.conference_assistant_plugin

import android.Manifest
import android.R.attr.x
import android.annotation.SuppressLint
import android.app.ActionBar
import android.app.ActivityManager
import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.pm.PackageManager
import android.content.pm.ServiceInfo
import android.content.pm.ServiceInfo.FOREGROUND_SERVICE_TYPE_MEDIA_PROJECTION
import android.content.pm.ServiceInfo.FOREGROUND_SERVICE_TYPE_MICROPHONE
import android.drm.DrmStore.Playback.STOP
import android.graphics.Color
import android.graphics.PixelFormat
import android.graphics.drawable.GradientDrawable
import android.icu.lang.UCharacter.GraphemeClusterBreak.L
import android.media.AudioManager
import android.os.Build
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import android.service.autofill.Validators.or
import android.telephony.PhoneStateListener
import android.telephony.TelephonyManager
import android.util.DisplayMetrics
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewConfiguration
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.FrameLayout
import android.widget.ImageButton
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.ScrollView
import android.widget.TextView
import android.widget.Toast
import androidx.annotation.RequiresPermission
import androidx.appcompat.app.AlertDialog
import androidx.core.app.ActivityCompat
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import androidx.core.app.ServiceCompat.startForeground
import androidx.core.content.ContextCompat
import androidx.core.content.ContextCompat.getSystemService
import androidx.core.view.isVisible
import androidx.core.view.updateLayoutParams
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleService
import androidx.lifecycle.lifecycleScope
import com.gankao.conference_assistant_plugin.FloatingWindowService
import com.gankao.conference_assistant_plugin.receiver.NativeReceiver
import com.gankao.conference_assistant_plugin.receiver.NotificationClickReceiver
import com.gankao.conference_assistant_plugin.speech.AliSpeechHelper
import com.gankao.conference_assistant_plugin.speech.AliSpeechHelper.ERROR
import com.gankao.conference_assistant_plugin.speech.AliSpeechHelper.RECORDING
import com.gankao.conference_assistant_plugin.speech.AliSpeechHelper.STOPPED
import com.gankao.conference_assistant_plugin.utils.Constant
import com.gankao.conference_assistant_plugin.utils.NativeBridge
import com.gankao.conference_assistant_plugin.utils.buildCornerDp
import com.gankao.conference_assistant_plugin.utils.buildSolid
import com.gankao.conference_assistant_plugin.utils.buildStrokeDp
import com.gankao.conference_assistant_plugin.widget.AlertDialogActivity
import com.gankao.conference_assistant_plugin.utils.sendNativeParams
import com.gk.logcat.LogUtil
import com.gk.logcat.LogUtil.context
import io.flutter.plugin.common.MethodChannel
import kotlinx.coroutines.CompletableDeferred
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlin.jvm.java

class FloatingWindowService : LifecycleService() {
    private lateinit var windowManager: WindowManager
    private lateinit var floatingView: View
    private lateinit var params: WindowManager.LayoutParams
    private var initialY: Int = 0
    private var initialX: Int = 0
    private var initialTouchY: Float = 0f
    private var isDragging = false
    private var isExpanded = false
    private lateinit var contentLayout: LinearLayout
    private lateinit var textView: TextView
    private var showNotification = false
    private var showFloatingWindow = false



    // 系统音频服务
    private var audioManager: AudioManager? = null

    // 音频焦点变化监听器
    private var focusChangeListener: AudioManager.OnAudioFocusChangeListener? = null

    // 电话服务
    private var telephonyManager: TelephonyManager? = null

    // 电话状态监听器
    private var phoneStateListener: PhoneStateListener? = null
    // AudioRecord相关
    /*
    private var audioRecord: AudioRecord? = null
    private val SAMPLE_RATE = 16000
    private val CHANNEL_CONFIG = AudioFormat.CHANNEL_IN_MONO
    private val AUDIO_FORMAT = AudioFormat.ENCODING_PCM_16BIT
    private var bufferSize = 0
    private var isRecording = false
    private var recordingThread: Thread? = null
    */
    private var screenWidth: Int = 0
    private var screenHeight: Int = 0
    private var initialWidth: Int = 0
    private var initialHeight: Int = 0
    private var initialTouchX: Float = 0f
    private var isResizing: Boolean = false
    var completableDeferredEnable: CompletableDeferred<Boolean>? = null


    val closeReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            if (intent?.action == "ACTION_CLOSE_FLOATING_WINDOW") {
//                intent.getBooleanExtra("stop", false)
//                showHideFloatingWindow(false)
            }
        }
    }
    companion object {
        private const val CHANNEL_ID = "floating_window_service"
        private const val NOTIFICATION_ID = 10002
        const val NOTIFICATION_ID_1 = 2000001
        const val NOTIFICATION_ID_2 = 2000002

        // 添加静态实例，方便通过静态方法访问当前服务实例
        private var instance: FloatingWindowService? = null
        
        // 提供静态方法获取服务实例
        fun getInstance(): FloatingWindowService? {
            return instance
        }
        
        // 提供静态方法更新文本
        fun updateTextStatic(text: String) {
            instance?.updateText(text)
        }

        fun appendTextStatic(text: String) {
            instance?.appendText(text)
        }
        
        // 控制悬浮窗显示
        fun showFloatingWindow(show: Boolean) {
            instance?.showHideFloatingWindow(show)
        }
        
        // 获取AudioRecord实例
        /*
        fun getAudioRecord(): AudioRecord? {
            return instance?.audioRecord
        }
        */
    }

    private val ACTION_NATIVE_SHOW_FLOATING_WINDOW = "com.gankao.conference_assistant_plugin.native_show_floating_window"
    private val nativeReceiver by lazy{
        NativeReceiver(){
            if (it == null){
                return@NativeReceiver
            }
            if (it.action == ACTION_NATIVE_SHOW_FLOATING_WINDOW){
                val show = it.getBooleanExtra("show", false)
                LogUtil.e("FloatingWindowService nativeReceiver show: $show")
                if (!Constant.isShowAlertDialogActivity){
                    showHideFloatingWindow(show)
                }
                Constant.isShowAlertDialogActivity = false
            }
        }
    }
//    private var notify1 : Notification? = null
    @SuppressLint("ClickableViewAccessibility")
    override fun onCreate() {
        super.onCreate()
        // 保存实例
        instance = this

        val intentFilter = IntentFilter()
        intentFilter.addAction(ACTION_NATIVE_SHOW_FLOATING_WINDOW)
        ContextCompat.registerReceiver(this, nativeReceiver, intentFilter, ContextCompat.RECEIVER_EXPORTED)

        // 注册
        registerReceiver(closeReceiver, IntentFilter("ACTION_CLOSE_FLOATING_WINDOW"),ContextCompat.RECEIVER_EXPORTED)




    val clickThreshold = ViewConfiguration.get(this).scaledTouchSlop
        Log.d("FloatingWindowService", "服务创建，保存实例")
        
        // 初始化AudioRecord
        // initAudioRecord()
        
        // 启动前台服务并显示通知
//        showForegroundNotification()

        // 初始化WindowManager
        windowManager = getSystemService(Context.WINDOW_SERVICE) as WindowManager
        val displayMetrics = DisplayMetrics()
        windowManager.defaultDisplay.getMetrics(displayMetrics)
        val height = displayMetrics.heightPixels/4
        screenHeight = displayMetrics.heightPixels
        screenWidth = displayMetrics.widthPixels
//        val maxTime = 180

//        notify1 = createNotify(NOTIFICATION_ID_1, "剩10分钟达录音上限，将自动停止", "点击继续录音")
//
        val maxTime = 1
        lifecycleScope.launch {
            launch {
                AliSpeechHelper.recordStateFlow.collect {
                    LogUtil.e("FloatingWindowService  recordStateFlow $it")
//                    if (it == STOPPED || it == ERROR){
//                        stopService()
//                    }

//                    if (!AliSpeechHelper.isUserStop && (it == STOPPED || it == ERROR)){
////                        剪流 AI 会议助手已自动结束录音
////                        共录音3小时
//                        val second = AliSpeechHelper.audioDurationStateFlow.value
//                        // 计算小时或者分钟
//                        val hour = second / 3600
//                        val minute = (second % 3600) / 60
//                        val time = if (hour>0) "${hour}小时${minute}分钟" else "${minute}分钟"
//                        if (ActivityCompat.checkSelfPermission(
//                                this@FloatingWindowService,
//                                Manifest.permission.POST_NOTIFICATIONS
//                            ) != PackageManager.PERMISSION_GRANTED
//                        ) {
//                            createNotify(NOTIFICATION_ID_2, "剪流AI会议助手已自动结束录音","共录音$time").let {
//                                NotificationManagerCompat.from(this@FloatingWindowService).notify(NOTIFICATION_ID_2, it)
//                            }
//                        }
//                    }
                }
            }
//            launch {
//                AliSpeechHelper.audioDurationStateFlow.collect { duration ->
//                    if (AliSpeechHelper.isBackground && duration == ((maxTime-0.5)*60).toInt()){
////                    剩10分钟达录音上限，将自动停止
////                    已录2小时50分钟，点此延长 1 小时
//                        createNotify(NOTIFICATION_ID_1, "剩10分钟达录音上限，将自动停止", "点击继续录音")
//                        LogUtil.e("FloatingWindowService", "剩10分钟达录音上限，将自动结束")
//                    } else if (AliSpeechHelper.isBackground && duration == maxTime*60){
//                        // 到达3小时上限自动结束
//                        LogUtil.e("FloatingWindowService", "自动结束")
//                        AliSpeechHelper.stopDialog()
//                    }
//                }
//            }

        }
        // 设置悬浮窗参数
        params = WindowManager.LayoutParams(
            screenWidth,  // 宽度
            height,  // 高度
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
            } else {
                WindowManager.LayoutParams.TYPE_PHONE
            },
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                    WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN or
                    WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS,
            PixelFormat.TRANSLUCENT
        )
        // 设置悬浮窗初始位置在屏幕底部
        params.gravity = Gravity.TOP or Gravity.START
        params.y = displayMetrics.heightPixels/2 - (height/2)  // 初始位置在屏幕底部



        // 加载悬浮窗布局
        val inflater = getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
        floatingView = inflater.inflate(R.layout.floating_window_layout, null)
        val linearLayout = floatingView.findViewById<FrameLayout>(R.id.linear_layout)
        linearLayout.background = GradientDrawable()
            .buildSolid(Color.parseColor("#CC1E1E1E"))
            .buildCornerDp(10f)
            .buildStrokeDp(1f, Color.parseColor("#80FFFFFF"))


        val flTop = floatingView.findViewById<FrameLayout>(R.id.flTop)
        val btnClose = floatingView.findViewById<ImageView>(R.id.btn_close)
        // 初始化视图组件
        contentLayout = floatingView.findViewById(R.id.content_layout)
        textView = floatingView.findViewById(R.id.text_view)


        // 设置返回应用按钮
        val btnBackToApp = floatingView.findViewById<ImageView>(R.id.btn_back_to_app)
        btnBackToApp.setOnClickListener {
            bringAppToFront()
        }

        val resizeHandle = floatingView.findViewById<View>(R.id.resize_handle)
        // 设置拖动区域
        contentLayout.setOnTouchListener { view, event ->
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    initialY = params.y
                    initialX = params.x
                    initialTouchY = event.rawY
                    initialTouchX = event.rawX
                    isDragging = true
                }
                MotionEvent.ACTION_MOVE -> {
                    if (isDragging) {
                        val deltaY = (event.rawY - initialTouchY).toInt()
                        val deltaX = (event.rawX - initialTouchX).toInt()


                        // 计算新的位置
                        var newY = initialY + deltaY
                        var newX = initialX + deltaX

                        // 边界检查 - 确保至少有一部分悬浮窗在屏幕内
                        // 保留至少25%的悬浮窗在屏幕内
                        val visiblePortion = 0.25

                        val minVisibleHeight = 200.coerceAtLeast((params.height * visiblePortion).toInt())
                        val minVisibleWidth = params.width * visiblePortion
                        // 顶部边界检查 - 不能超出屏幕顶部太多
                        newY = newY.coerceAtLeast(-params.height + (minVisibleHeight).toInt())

                        // 底部边界检查 - 不能超出屏幕底部太多
                        newY = newY.coerceAtMost(screenHeight - (params.height * visiblePortion).toInt())

                        // 左侧边界检查 - 不能超出屏幕左侧太多
                        newX = newX.coerceAtLeast(-params.width + (minVisibleWidth).toInt())

                        // 右侧边界检查 - 不能超出屏幕右侧太多
                        newX = newX.coerceAtMost(screenWidth - (minVisibleWidth).toInt())
                        params.y = newY
                        params.x = newX
                        windowManager.updateViewLayout(floatingView, params)
                    }
                }
                MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                    isDragging = false
                    val upX = event.rawX
                    val upY = event.rawY
                    val dx = Math.abs(upX - initialTouchX)
                    val dy = Math.abs(upY - initialTouchY)
                    if (dx < clickThreshold && dy < clickThreshold) {
                        // ✅ 认为是点击事件
                        Log.d("TouchTest", "点击事件！ dy: $dy clickThreshold:$clickThreshold")
                        // 这里触发你要的点击操作
                        flTop.isVisible = true
                        lifecycleScope.launch {
                            withContext(Dispatchers.IO) {
                                delay(3000)
                            }
                            flTop.isVisible = false
                        }
                    }
                }
            }
            true
        }


        // 设置调整大小的触摸监听
        resizeHandle.setOnTouchListener { _, event ->
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    Log.e("TAG", "onCreate: resizeHandle ACTION_DOWN : ${event.action}  ${params.height}  ${params.width}")
                    // 记录初始状态
                    initialWidth = params.width
                    initialHeight = params.height
                    initialTouchX = event.rawX
                    initialTouchY = event.rawY
                    isResizing = true

                }
                MotionEvent.ACTION_MOVE -> {
                    Log.e("TAG", "onCreate: resizeHandle ACTION_MOVE : ${event.action}")
                    if (isResizing) {
                        // 计算新的宽度和高度
                        val deltaX = (initialTouchX - event.rawX).toInt()
                        val deltaY = (event.rawY - initialTouchY).toInt()

                        // 更新宽度（注意，向左拖动时增加宽度）
                        var newWidth = initialWidth - deltaX
                        // 应用宽度限制
                        val minWidth = screenWidth * 2 / 3
                        val maxWidth = screenWidth
                        newWidth = newWidth.coerceIn(minWidth, maxWidth)

                        // 更新高度
                        var newHeight = initialHeight + deltaY
                        // 应用高度限制
                        val minHeight = screenHeight / 8
                        val maxHeight = screenHeight / 3
                        newHeight = newHeight.coerceIn(minHeight, maxHeight)
                        Log.e("TAG", "onCreate: resizeHandle======== $newWidth $newHeight" )
                        // 更新布局参数
                        params.width = newWidth
                        params.height = newHeight
                        windowManager.updateViewLayout(floatingView, params)

                    }
                }
                MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                    isResizing = false
                }
            }
            true
        }
        
        // 设置点击事件
//        floatingView.setOnClickListener {
////            // 通知Flutter端打开主Activity
//////            channel?.invokeMethod("openMainActivity", null)
////            // 同时尝试直接通过Intent打开应用
//////            bringAppToFront()
//            flTop.isVisible = true
//            lifecycleScope.launch {
//                withContext(Dispatchers.IO) {
//                    delay(3000)
//                }
//                flTop.isVisible = false
//            }
//        }
        btnClose.setOnClickListener {
            LogUtil.e("btnClose click")
            // 使用系统默认的确认对话框
            showHideFloatingWindow(false)
            val intent = Intent(this, AlertDialogActivity::class.java).apply {
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            startActivity(intent)
        }
        
        // 默认不显示悬浮窗，只在应用进入后台时显示
        showHideFloatingWindow(false)
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        super.onStartCommand(intent, flags, startId)
        // 检查intent中是否有显示悬浮窗的指令
        val shouldShowFloatingWindow = intent?.getBooleanExtra("show_floating_window", false) ?: false
        val audioSourceType = intent?.getIntExtra("audioSourceType", 1) ?:1
        showForegroundNotification(audioSourceType)
        showHideFloatingWindow(shouldShowFloatingWindow)
        checkAudioFocus(audioSourceType)

        Log.d("FloatingWindowService", "服务已启动为前台服务，显示悬浮窗: $shouldShowFloatingWindow audioSourceType:$audioSourceType")
        return START_STICKY
    }


    override fun onTaskRemoved(rootIntent: Intent?) {
        super.onTaskRemoved(rootIntent)
        LogUtil.e("onTaskRemoved 任务被移除了")
        NativeBridge.invokeNativeMethod("invokeFlutterMethod", "onTaskRemoved")
        AliSpeechHelper.stopDialog(isUser = true)
    }

    override fun onBind(intent: Intent): IBinder? {
        return super.onBind(intent)
    }

    override fun onDestroy() {
        super.onDestroy()
        unregisterReceiver(nativeReceiver)
        unregisterReceiver(closeReceiver)
        if (::floatingView.isInitialized) {
            try {
                windowManager.removeView(floatingView)
            } catch (e: Exception) {
                // 忽略可能的异常
            }
        }
        LogUtil.e("FloatingWindowService stopService")
        stopService()
        // 释放AudioRecord资源
        // releaseAudioRecord()
        
        // 清除实例
        if (instance == this) {
            instance = null
        }
    }

    fun appendText(text: String){
        Log.d("FloatingWindowService", "开始更新文本 appendText: $text, textView是否初始化: ${::textView.isInitialized}")
        Handler(Looper.getMainLooper()).post {
            if (::textView.isInitialized){
                textView.append("\n"+text)
                Handler(Looper.getMainLooper()).postDelayed({
                    val scrollView = floatingView.findViewById<ScrollView>(R.id.scroll_view)
                    scrollView.fullScroll(View.FOCUS_DOWN)
                }, 100)
            }
        }
    }
    fun updateText(text: String) {
        Log.d("FloatingWindowService", "开始更新文本 updateText: $text, textView是否初始化: ${::textView.isInitialized}")
        if (text.isBlank()){
            return
        }
        Handler(Looper.getMainLooper()).post {
            try {
                if (::textView.isInitialized) {
                    textView.text = text
                    Log.d("FloatingWindowService", "更新文本成功: $text")
                    
                    // 找到ScrollView并滚动到底部
                    try {
                        val scrollView = floatingView.findViewById<ScrollView>(R.id.scroll_view)
                        if (scrollView != null) {
                            Log.d("FloatingWindowService", "找到ScrollView，准备滚动")
                            
                            // 使用延迟确保在布局完成后滚动
                            Handler(Looper.getMainLooper()).postDelayed({
                                try {
                                    // 尝试使用多种方式滚动到底部
                                    scrollView.fullScroll(ScrollView.FOCUS_DOWN)
                                    
                                    // 额外设置滚动位置以确保滚动到底部
                                    val childHeight = textView.height
                                    val scrollHeight = scrollView.height
                                    if (childHeight > scrollHeight) {
                                        scrollView.scrollTo(0, childHeight - scrollHeight)
                                    }
                                    
                                    Log.d("FloatingWindowService", "已执行滚动到底部操作")
                                } catch (e: Exception) {
                                    Log.e("FloatingWindowService", "滚动到底部操作失败: ${e.message}")
                                }
                            }, 100) // 延迟100毫秒确保布局已完成
                        } else {
                            Log.e("FloatingWindowService", "找不到ID为scroll_view的ScrollView")
                        }
                    } catch (e: Exception) {
                        Log.e("FloatingWindowService", "查找ScrollView失败: ${e.message}")
                    }
                } else {
                    Log.e("FloatingWindowService", "文本视图未初始化")
                }
            } catch (e: Exception) {
                Log.e("FloatingWindowService", "更新文本出错: ${e.message}, ${e.stackTraceToString()}")
            }
        }
    }

    fun setMethodChannel(channel: MethodChannel) {
//        this.channel = channel
    }

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                "Floating Window Service",
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                enableLights(true)
                enableVibration(true)
            }
            channel.description = "用于录音时显示通知"
            val notificationManager = getSystemService(NotificationManager::class.java)
            notificationManager.createNotificationChannel(channel)
        }
    }

    // 显示或隐藏悬浮窗
    fun showHideFloatingWindow(show: Boolean) {
        if (showFloatingWindow == show){
            return
        }
        if (show && AliSpeechHelper.recordStateFlow.value != RECORDING){
            return
        }
        showFloatingWindow = show

        if (show) {
            // 显示悬浮窗
            if (::floatingView.isInitialized && ::windowManager.isInitialized) {
                try {
                    // 先尝试移除之前的视图（如果有）
//                    try {
//                        windowManager.removeView(floatingView)
//                    } catch (e: Exception) {
//                        // 忽略可能的异常，因为视图可能尚未添加
//                    }
                    
                    // 添加悬浮窗到窗口
                    windowManager.addView(floatingView, params)
                    Log.d("FloatingWindowService", "悬浮窗已显示")
                } catch (e: Exception) {
                    Log.e("FloatingWindowService", "显示悬浮窗出错: ${e.message}")
                }
            } else {
                Log.e("FloatingWindowService", "悬浮窗视图未初始化，无法显示")
            }
        } else {
            // 隐藏悬浮窗
            if (::floatingView.isInitialized && ::windowManager.isInitialized) {
                try {
                    windowManager.removeView(floatingView)
                    Log.d("FloatingWindowService", "悬浮窗已隐藏")
                } catch (e: Exception) {
                    Log.e("FloatingWindowService", "隐藏悬浮窗出错: ${e.message}")
                }
            }
        }
    }

    // 显示前台服务通知
    private fun showForegroundNotification(audioSourceType: Int) {
        // 创建前台服务通知
        createNotificationChannel()
        
        // 创建用于返回应用的Intent


        val pendingIntent = NativeBridge.getFlutterIntentViaReflection(this, "/recording").let {
            if (it != null){
                LogUtil.e("FloatingWindowService", "创建 native PendingIntent 成功")
                PendingIntent.getActivity(this, 0, it, (if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M)
                    PendingIntent.FLAG_IMMUTABLE or PendingIntent.FLAG_UPDATE_CURRENT
                else
                    PendingIntent.FLAG_UPDATE_CURRENT))
            }else{
                null
            }
        } ?: run {
            LogUtil.e("FloatingWindowService", "创建 native PendingIntent 失败，创建广播PendingIntent")
            val clickIntent = Intent(this, NotificationClickReceiver::class.java)
            PendingIntent.getBroadcast(
                this,
                0,
                clickIntent,
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M)
                    PendingIntent.FLAG_IMMUTABLE or PendingIntent.FLAG_UPDATE_CURRENT
                else
                    PendingIntent.FLAG_UPDATE_CURRENT
            )
        }

        // 创建前台通知
        val notification = NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("剪流AI会议助理.录音中")
            .setContentText("点击返回应用")
            .setSmallIcon(android.R.drawable.ic_dialog_info)
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setDefaults(NotificationCompat.DEFAULT_ALL)
            .setOngoing(true)
            .setContentIntent(pendingIntent)
            .build()
        
        // 启动前台服务
        LogUtil.e("startForeground=========== audioSourceType:$audioSourceType ${System.currentTimeMillis()}")
        if (audioSourceType == 2){
            startForeground(NOTIFICATION_ID, notification,   FOREGROUND_SERVICE_TYPE_MICROPHONE or FOREGROUND_SERVICE_TYPE_MEDIA_PROJECTION)
            AliSpeechHelper.notifyCompletableDeferredEnable?.complete(true)
        }else{
            startForeground(NOTIFICATION_ID, notification,  FOREGROUND_SERVICE_TYPE_MICROPHONE)
        }
//        startForeground(NOTIFICATION_ID, notification)
    }

    // 将应用从后台切换到前台
    private fun bringAppToFront() {
        try {
            val activityManager = getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                val appTasks = activityManager.appTasks
                if (appTasks.isNotEmpty()) {
                    appTasks[0].moveToFront()
                    Log.d("AppRestore", "bringAppToFront App 成功从后台唤醒到前台")
                } else {
                    Log.w("AppRestore", "bringAppToFront 找不到 App 任务")
                }
            }
            showHideFloatingWindow(false)
        } catch (e: Exception) {
            LogUtil.e("FloatingWindowService", "bringAppToFront 方法1失败: ${e.message}")
        }
//        try {
//            // 方法1: 使用包名获取启动Intent
//            val launchIntent = packageManager.getLaunchIntentForPackage(packageName)
//            if (launchIntent != null) {
//                launchIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_REORDER_TO_FRONT)
//                startActivity(launchIntent)
//                Log.d("FloatingWindowService", "方法1将应用切换到前台")
//                return
//            }
//        } catch (e: Exception) {
//            Log.e("FloatingWindowService", "方法1失败: ${e.message}")
//        }

//        try {
//            // 方法2: 使用ACTION_MAIN和CATEGORY_LAUNCHER
//            val intent = Intent(Intent.ACTION_MAIN)
//            intent.addCategory(Intent.CATEGORY_LAUNCHER)
//            intent.setPackage(packageName)
//            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_REORDER_TO_FRONT)
//            startActivity(intent)
//            Log.d("FloatingWindowService", "方法2将应用切换到前台")
//            return
//        } catch (e: Exception) {
//            Log.e("FloatingWindowService", "方法2失败: ${e.message}")
//        }

//        try {
//            // 方法3: 尝试获取最近的activity
//            val intent = Intent(applicationContext, Class.forName("$packageName.MainActivity"))
//            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_REORDER_TO_FRONT)
//            startActivity(intent)
//            Log.d("FloatingWindowService", "方法3将应用切换到前台")
//        } catch (e: Exception) {
//            Log.e("FloatingWindowService", "方法3失败: ${e.message}")
//        }
    }

    fun stopService(){
        // 停止前台服务
        LogUtil.e("FloatingWindowService stopService")
        try {
            stopForeground(true)
            stopSelf()
            getSystemService(NotificationManager::class.java)?.cancel(NOTIFICATION_ID)
        } catch (e: Exception) {
            LogUtil.e("FloatingWindowService stopService e:${e.message}")
        }

    }

    private fun checkAudioFocus(audioSourceType: Int){
        // 获取系统音频管理器
        if (audioManager != null){
            return
        }
        audioManager = getSystemService(Context.AUDIO_SERVICE) as? AudioManager
        val audioManager = this.audioManager
        if (audioManager == null){
            return
        }
        // 配置音频焦点监听器
        focusChangeListener = AudioManager.OnAudioFocusChangeListener { focusChange ->
            when (focusChange) {
                AudioManager.AUDIOFOCUS_LOSS_TRANSIENT -> {
                    LogUtil.i("FloatingWindowService, checkAudioFocus focusChangeListener AUDIOFOCUS_LOSS_TRANSIENT 临时丢失焦点，暂停录音")
                    if (AliSpeechHelper.isInvokeFocusBehavior){
                        AliSpeechHelper.pause() // 临时丢失焦点，暂停录音
                    }

                }
                AudioManager.AUDIOFOCUS_GAIN -> {
                    LogUtil.i("FloatingWindowService, checkAudioFocus focusChangeListener AUDIOFOCUS_GAIN 重新获得焦点，恢复录音")
                    if (AliSpeechHelper.isInvokeFocusBehavior){
                        AliSpeechHelper.resume()          // 重新获得焦点，恢复录音
                    }
                }
                AudioManager.AUDIOFOCUS_LOSS -> {
                    LogUtil.i("FloatingWindowService, checkAudioFocus focusChangeListener AUDIOFOCUS_LOSS 长时间失去焦点，停止录音")
                    if (AliSpeechHelper.isInvokeFocusBehavior){
                        AliSpeechHelper.stopDialog()  // 长时间失去焦点，停止录音
                    }
                }
            }
        }

        if (audioSourceType == 1){
            // 请求短暂音频焦点
            val result = audioManager.requestAudioFocus(
                focusChangeListener,
                AudioManager.STREAM_MUSIC,
                AudioManager.AUDIOFOCUS_GAIN_TRANSIENT
            )
            // 如果未获得音频焦点
            if (result != AudioManager.AUDIOFOCUS_REQUEST_GRANTED) {
                LogUtil.i("FloatingWindowService, checkAudioFocus 未获得音频焦点")
            }
        }

        // 设置电话状态监听器，监听来电和通话状态
        telephonyManager = getSystemService(Context.TELEPHONY_SERVICE) as TelephonyManager
        phoneStateListener = object : PhoneStateListener() {
            override fun onCallStateChanged(state: Int, phoneNumber: String?) {
                when (state) {
                    TelephonyManager.CALL_STATE_RINGING -> {
                        LogUtil.i("FloatingWindowService, checkAudioFocus phoneStateListener CALL_STATE_RINGING 来电时暂停录音")
                        if (AliSpeechHelper.isInvokeFocusBehavior){
                            AliSpeechHelper.pause() // 来电时暂停录音
                        }
                    }
                    TelephonyManager.CALL_STATE_IDLE -> {
                        LogUtil.i("FloatingWindowService, checkAudioFocus phoneStateListener CALL_STATE_IDLE 通话结束后恢复录音")
                        if (AliSpeechHelper.isInvokeFocusBehavior){
                            AliSpeechHelper.resume()   // 通话结束后恢复录音
                        }
                    }
                }
                super.onCallStateChanged(state, phoneNumber)
            }
        }
        try {
            telephonyManager?.listen(phoneStateListener, PhoneStateListener.LISTEN_CALL_STATE)
        } catch (e: Exception) {
            LogUtil.i("FloatingWindowService, telephonyManager: ${e.message}")
        }

    }


    private fun createNotify(notificationId: Int, title: String, content: String):Notification{
        val intent = getFlutterPendingIntent("/home")
        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle(title)
            .setContentText(content)
            .setSmallIcon(android.R.drawable.ic_dialog_info)
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setDefaults(NotificationCompat.DEFAULT_ALL)
            .setOngoing(true)
            .setContentIntent(intent)
            .build()
//        if (ActivityCompat.checkSelfPermission(
//                this,
//                Manifest.permission.POST_NOTIFICATIONS
//            ) != PackageManager.PERMISSION_GRANTED
//        ) {
//
//
//        }
//        NotificationManagerCompat.from(this).notify(notificationId, notification)

    }


    private fun getFlutterPendingIntent(route: String): PendingIntent{
        return NativeBridge.getFlutterIntentViaReflection(this, route).let {
            if (it != null){
                LogUtil.e("FloatingWindowService", "创建 native PendingIntent 成功")
                PendingIntent.getActivity(this, 0, it, (if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M)
                    PendingIntent.FLAG_IMMUTABLE or PendingIntent.FLAG_UPDATE_CURRENT
                else
                    PendingIntent.FLAG_UPDATE_CURRENT))
            }else{
                null
            }
        } ?: run {
            LogUtil.e("FloatingWindowService", "创建 native PendingIntent 失败，创建广播PendingIntent")
            val clickIntent = Intent(this, NotificationClickReceiver::class.java)
            PendingIntent.getBroadcast(
                this,
                0,
                clickIntent,
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M)
                    PendingIntent.FLAG_IMMUTABLE or PendingIntent.FLAG_UPDATE_CURRENT
                else
                    PendingIntent.FLAG_UPDATE_CURRENT
            )
        }
    }


//    fun notify1(NOTIFICATION_ID_1: Int){
//        if (ActivityCompat.checkSelfPermission(
//                this,
//                Manifest.permission.POST_NOTIFICATIONS
//            ) != PackageManager.PERMISSION_GRANTED
//        ) {
//            return
//        }
////        notify1?.let { NotificationManagerCompat.from(this).notify(NOTIFICATION_ID_1, it) }
//    }
} 