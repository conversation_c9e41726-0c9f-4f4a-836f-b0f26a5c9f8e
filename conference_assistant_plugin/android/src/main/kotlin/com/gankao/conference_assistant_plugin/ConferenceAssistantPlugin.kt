package com.gankao.conference_assistant_plugin

import android.Manifest
import android.R.attr.duration
import android.R.attr.path
import android.R.attr.text
import android.R.attr.type
import android.app.Activity
import android.app.ActivityManager
import android.content.ContentValues
import android.net.Uri
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.provider.MediaStore
import androidx.annotation.NonNull
import com.gankao.conference_assistant_plugin.utils.AudioRecordUtil
import io.flutter.embedding.engine.plugins.FlutterPlugin
import io.flutter.plugin.common.EventChannel
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import android.os.Environment
import io.flutter.plugin.common.MethodChannel.MethodCallHandler
import io.flutter.plugin.common.MethodChannel.Result
import io.flutter.embedding.engine.plugins.activity.ActivityAware
import io.flutter.embedding.engine.plugins.activity.ActivityPluginBinding
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import com.gk.logcat.LogUtil
import java.io.File
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Build.VERSION_CODES.P
import com.gankao.conference_assistant_plugin.AudioRMSVolumeStreamHandler
import com.gankao.conference_assistant_plugin.AudioStreamHandler
import com.gankao.conference_assistant_plugin.RecordingCompletionHandler
import com.gankao.conference_assistant_plugin.SpeechResultHandler
import com.gankao.conference_assistant_plugin.VolumeStreamHandler
import com.gankao.conference_assistant_plugin.speech.AliSpeechHelper
import com.gankao.conference_assistant_plugin.speech.AliSpeechUploadHelper
import com.gankao.conference_assistant_plugin.speech.ContentModel
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.flow.collectLatest
import android.provider.Settings
import android.util.Log
import androidx.core.app.ActivityCompat
import com.gankao.conference_assistant_plugin.AudioDurationStreamHandler
import com.gankao.conference_assistant_plugin.AudioUrlStreamHandler
import com.gankao.conference_assistant_plugin.DialogContentStreamHandler
import com.gankao.conference_assistant_plugin.FloatingWindowService.Companion.NOTIFICATION_ID_1
import com.gankao.conference_assistant_plugin.FloatingWindowService.Companion.NOTIFICATION_ID_2
import com.gankao.conference_assistant_plugin.RecordStateStreamHandler
import com.gankao.conference_assistant_plugin.api.Api
import com.gankao.conference_assistant_plugin.api.Api.URL_TEST
import com.gankao.conference_assistant_plugin.speech.AliSpeechHelper.ERROR
import com.gankao.conference_assistant_plugin.speech.AliSpeechHelper.PAUSED
import com.gankao.conference_assistant_plugin.speech.AliSpeechHelper.RECORDING
import com.gankao.conference_assistant_plugin.speech.AliSpeechHelper.STOPPED
import com.gankao.conference_assistant_plugin.speech.AliSpeechHelper.isBackground
import com.gankao.conference_assistant_plugin.speech.RecordInfoModel
import com.gankao.conference_assistant_plugin.utils.Constant
import com.gankao.conference_assistant_plugin.utils.DebouncedMessageWatcher
import com.gankao.conference_assistant_plugin.utils.MMKVHelper
import com.gankao.conference_assistant_plugin.utils.MemoryUtils
import com.gankao.conference_assistant_plugin.utils.PermissionUtils.hasManageExternalStoragePermission
import com.gankao.conference_assistant_plugin.utils.TimeUtils
import com.gankao.conference_assistant_plugin.utils.sendRecording
import com.gankao.network.http.request
import kotlinx.coroutines.CompletableDeferred
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.withContext
import java.net.URLEncoder
import java.util.HashMap
import kotlin.collections.getOrDefault
import kotlin.jvm.java

/** ConferenceAssistantPlugin */
private const val TAG = "ConferenceAssistantPlug"
class ConferenceAssistantPlugin: FlutterPlugin, MethodCallHandler, ActivityAware {
  /// The MethodChannel that will the communication between Flutter and native Android
  ///
  /// This local reference serves to register the plugin with the Flutter Engine and unregister it
  /// when the Flutter Engine is detached from the Activity
  private lateinit var channel: MethodChannel
  private lateinit var audioEventChannel: EventChannel
  private lateinit var recordingCompletionEventChannel: EventChannel
  private var audioRecordUtil: AudioRecordUtil? = null
  private var audioStreamHandler: AudioStreamHandler? = null
  private var recordingCompletionHandler: RecordingCompletionHandler? = null
  private lateinit var volumeEventChannel: EventChannel
  private var volumeStreamHandler: VolumeStreamHandler? = null
  private val coroutineScope = CoroutineScope(Dispatchers.Main + Job())
  private lateinit var context: Context
  private var activity: Activity? = null
  private var binding: ActivityPluginBinding? = null
  private lateinit var speechRecognitionChannel: MethodChannel
  private lateinit var speechRecognitionResultChannel: EventChannel
  private var speechResultHandler: SpeechResultHandler? = null
  private lateinit var audioRMSVolumeEventChannel: EventChannel
  private var audioRMSVolumeStreamHandler: AudioRMSVolumeStreamHandler? = null
  private var floatingWindowService: FloatingWindowService? = null
  private lateinit var floatingWindowChannel: MethodChannel
  private var speechJob: Job? = null
  private var submitMeetingJob: Job? = null
  private lateinit var audioUrlEventChannel: EventChannel
  private var audioUrlStreamHandler: AudioUrlStreamHandler? = null
  private lateinit var recordStateEventChannel: EventChannel
  private var recordStateStreamHandler: RecordStateStreamHandler? = null
  private lateinit var dialogContentEventChannel: EventChannel
  private var dialogContentStreamHandler: DialogContentStreamHandler? = null
  private lateinit var audioDurationEventChannel: EventChannel
  private var audioDurationStreamHandler: AudioDurationStreamHandler? = null
  private lateinit var uploadStatusEventChannel: EventChannel
  private var uploadStatusStreamHandler: UploadStatusStreamHandler? = null
  private lateinit var uploadProgressEventChannel: EventChannel
  private var uploadProgressStreamHandler: UploadProgressStreamHandler? = null

  override fun onAttachedToEngine(flutterPluginBinding: FlutterPlugin.FlutterPluginBinding) {
    context = flutterPluginBinding.applicationContext
    MMKVHelper.init(context)
    channel = MethodChannel(flutterPluginBinding.binaryMessenger, "conference_assistant_plugin")
    channel.setMethodCallHandler(this)


    // 添加悬浮窗通道
    floatingWindowChannel = MethodChannel(flutterPluginBinding.binaryMessenger, "com.gankao.conference_assistant_plugin/floating_window")
    floatingWindowChannel.setMethodCallHandler { call, result ->
      when (call.method) {
        "checkOverlayPermission" -> {
          if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            result.success(Settings.canDrawOverlays(context))
          } else {
            result.success(true)
          }
        }
        "startFloatingWindow" -> {
          try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
              if (!Settings.canDrawOverlays(context)) {
                android.util.Log.d("ConferenceAssistantPlugin", "启动悬浮窗服务 申请 ACTION_MANAGE_OVERLAY_PERMISSION")
                val intent = Intent(
                  Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
                  Uri.parse("package:${context.packageName}")
                )
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_MULTIPLE_TASK)
                // 设置窗口显示在底部
                intent.putExtra("android.settings.extra.INITIAL_URI", Uri.parse("package:${context.packageName}"))
                intent.putExtra("android.settings.extra.INITIAL_POSITION", 0) // 0 表示底部
                context.startActivity(intent)
                result.success(false)
              } else {
                // 启动悬浮窗服务
//                val serviceIntent = Intent(context, FloatingWindowService::class.java)
//                serviceIntent.putExtra("show_floating_window", true)
//                context.startService(serviceIntent)
//
//                // 通过静态方法获取服务实例
//                floatingWindowService = FloatingWindowService.getInstance()
//                floatingWindowService?.setMethodChannel(floatingWindowChannel)
                floatingWindowService?.showHideFloatingWindow(true)
                android.util.Log.d("ConferenceAssistantPlugin", "启动悬浮窗服务成功，服务实例: ${floatingWindowService}")
                result.success(true)
              }
            } else {
              // 启动悬浮窗服务
              val serviceIntent = Intent(context, FloatingWindowService::class.java)
              serviceIntent.putExtra("show_floating_window", true)
              context.startForegroundService(serviceIntent)
              
              // 通过静态方法获取服务实例
              floatingWindowService = FloatingWindowService.getInstance()
              floatingWindowService?.setMethodChannel(floatingWindowChannel)
              
              android.util.Log.d("ConferenceAssistantPlugin", "启动悬浮窗服务成功，服务实例: ${floatingWindowService}")
              result.success(true)
            }
          } catch (e: Exception) {
            android.util.Log.e("ConferenceAssistantPlugin", "启动悬浮窗服务出错: ${e.message}")
            result.error("START_FLOATING_WINDOW_ERROR", "启动悬浮窗服务出错: ${e.message}", null)
          }
        }
        "minimizeApp" -> {
          val startMain = Intent(Intent.ACTION_MAIN)
          startMain.addCategory(Intent.CATEGORY_HOME)
          startMain.flags = Intent.FLAG_ACTIVITY_NEW_TASK
          context.startActivity(startMain)
          result.success(null)
        }
        "showFloatingWindow" -> {
          try {
            val show = call.arguments as Boolean
            android.util.Log.d("ConferenceAssistantPlugin", "设置悬浮窗显示状态: $show")
            
            // 使用静态方法控制悬浮窗显示
            FloatingWindowService.showFloatingWindow(show)
            
            result.success(null)
          } catch (e: Exception) {
            android.util.Log.e("ConferenceAssistantPlugin", "设置悬浮窗显示状态出错: ${e.message}")
            result.error("SHOW_FLOATING_WINDOW_ERROR", "设置悬浮窗显示状态出错: ${e.message}", null)
          }
        }
        "enableBackgroundRecognition" -> {
          try {
            val enable = call.arguments as Boolean
            // 调用启用或禁用后台录音的方法
            if (enable) {
              // 确保悬浮窗服务保持运行状态
              if (floatingWindowService == null) {
                val serviceIntent = Intent(context, FloatingWindowService::class.java)
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                  context.startForegroundService(serviceIntent)
                } else {
                  context.startService(serviceIntent)
                }
                floatingWindowService = FloatingWindowService.getInstance()
                floatingWindowService?.setMethodChannel(floatingWindowChannel)
                android.util.Log.d("ConferenceAssistantPlugin", "启动悬浮窗前台服务以保持后台录音")
              }
              
              // 向语音识别模块指示启用后台模式
              AliSpeechHelper.enableBackgroundRecognition(true)
              android.util.Log.d("ConferenceAssistantPlugin", "已启用后台语音识别")
            } else {
              // 禁用后台模式
              AliSpeechHelper.enableBackgroundRecognition(false)
              android.util.Log.d("ConferenceAssistantPlugin", "已禁用后台语音识别")
            }
            result.success(true)
          } catch (e: Exception) {
            android.util.Log.e("ConferenceAssistantPlugin", "启用/禁用后台识别出错: ${e.message}")
            result.error("BACKGROUND_RECOGNITION_ERROR", "启用/禁用后台识别出错: ${e.message}", null)
          }
        }
        else -> result.notImplemented()
      }
    }
    
    // 添加文件打开器通道
    val fileOpenerChannel = MethodChannel(flutterPluginBinding.binaryMessenger, "com.gankao.conference_assistant_plugin/file_opener")
    fileOpenerChannel.setMethodCallHandler { call, result ->
      if (call.method == "openFolder") {
        val path = call.argument<String>("path")
        if (path != null) {
          openFolder(path, result)
        } else {
          result.error("INVALID_ARGUMENT", "Folder path is required", null)
        }
      } else {
        result.notImplemented()
      }
    }
    
    // Setup event channel for audio data streaming
    audioStreamHandler = AudioStreamHandler()
    audioEventChannel = EventChannel(flutterPluginBinding.binaryMessenger, "conference_assistant_plugin/audio_stream")
    audioEventChannel.setStreamHandler(audioStreamHandler)
    
    // Setup event channel for recording completion
    recordingCompletionHandler = RecordingCompletionHandler()
    recordingCompletionEventChannel = EventChannel(flutterPluginBinding.binaryMessenger, "conference_assistant_plugin/recording_completion")
    recordingCompletionEventChannel.setStreamHandler(recordingCompletionHandler)
    
    // Setup event channel for volume monitoring
    volumeStreamHandler = VolumeStreamHandler()
    volumeEventChannel = EventChannel(flutterPluginBinding.binaryMessenger, "conference_assistant_plugin/volume")
    volumeEventChannel.setStreamHandler(volumeStreamHandler)
    
    // Setup event channel for audio RMS volume
    audioRMSVolumeStreamHandler = AudioRMSVolumeStreamHandler()
    audioRMSVolumeEventChannel = EventChannel(flutterPluginBinding.binaryMessenger, "com.gankao.conference_assistant_plugin/audio_rms_volume")
    audioRMSVolumeEventChannel.setStreamHandler(audioRMSVolumeStreamHandler)
    
    // Setup event channel for audio URL
    audioUrlStreamHandler = AudioUrlStreamHandler()
    audioUrlEventChannel = EventChannel(flutterPluginBinding.binaryMessenger, "com.gankao.conference_assistant_plugin/audio_url")
    audioUrlEventChannel.setStreamHandler(audioUrlStreamHandler)
    
    // Setup event channel for record state
    recordStateStreamHandler = RecordStateStreamHandler()
    recordStateEventChannel = EventChannel(flutterPluginBinding.binaryMessenger, "com.gankao.conference_assistant_plugin/record_state")
    recordStateEventChannel.setStreamHandler(recordStateStreamHandler)
    
    // Setup event channel for dialog content
    dialogContentStreamHandler = DialogContentStreamHandler()
    dialogContentEventChannel = EventChannel(flutterPluginBinding.binaryMessenger, "com.gankao.conference_assistant_plugin/dialog_content")
    dialogContentEventChannel.setStreamHandler(dialogContentStreamHandler)
    
    // Setup event channel for audio duration
    audioDurationStreamHandler = AudioDurationStreamHandler()
    audioDurationEventChannel = EventChannel(flutterPluginBinding.binaryMessenger, "com.gankao.conference_assistant_plugin/audio_duration")
    audioDurationEventChannel.setStreamHandler(audioDurationStreamHandler)

    // Setup event channel for upload status
    uploadStatusStreamHandler = UploadStatusStreamHandler()
    uploadStatusEventChannel = EventChannel(flutterPluginBinding.binaryMessenger, "com.gankao.conference_assistant_plugin/upload_status")
    uploadStatusEventChannel.setStreamHandler(uploadStatusStreamHandler)

    // Setup event channel for upload progress
    uploadProgressStreamHandler = UploadProgressStreamHandler()
    uploadProgressEventChannel = EventChannel(flutterPluginBinding.binaryMessenger, "com.gankao.conference_assistant_plugin/upload_progress")
    uploadProgressEventChannel.setStreamHandler(uploadProgressStreamHandler)

    // 添加语音识别通道
    speechRecognitionChannel = MethodChannel(flutterPluginBinding.binaryMessenger, "com.gankao.conference_assistant_plugin/speech_recognition")
    speechRecognitionChannel.setMethodCallHandler { call, result ->
      when (call.method) {
        "checkDeviceMemory"->{
          try {
            val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            val memoryInfo = ActivityManager.MemoryInfo()
            activityManager.getMemoryInfo(memoryInfo)

            val availMem = memoryInfo.availMem       // 可用内存（字节）
            val totalMem = memoryInfo.totalMem       // 总内存（字节）
            LogUtil.i("flutter 调用 checkDeviceMemory: lowMemory:${memoryInfo.lowMemory}")
            val isLow = memoryInfo.lowMemory || MemoryUtils.isAppMemoryTooHigh(context)
            result.success(isLow)
          }catch (e: Exception){
            result.error("checkDeviceMemory", "Failed to checkDeviceMemory status: ${e.message}", null)
          }
        }
        "setBackgroundRecognition" -> {
          try {
            // 设置当前识别实在后台还是前台
            val isBackground = call.arguments as Boolean
            AliSpeechHelper.isBackground = isBackground
            LogUtil.i("flutter 调用 setBackgroundRecognition: $isBackground")
            result.success(isBackground)
          } catch (e: Exception) {
            result.error("BACKGROUND_STATUS_ERROR", "Failed to get background recognition status: ${e.message}", null)
          }
        }
        "fetchCommonParams" -> {
          try {
            val params = call.argument<Map<String, Any>>("params")
            LogUtil.i("flutter 调用 fetchCommonParams,  params: $params")
            val cookie = params?.getOrDefault("cookie","").toString() ?: ""
            val jwtoken = params?.getOrDefault("jwtoken","").toString() ?: ""
            val baseUrl = params?.getOrDefault("baseUrl",Api.URL_TEST).toString() ?: Api.URL_TEST
            val headerMap = mutableMapOf("cookie" to cookie, "jwtoken" to jwtoken)
            val uploadingRecordings = params?.getOrDefault("uploadingRecordings", listOf<Map<String, Any>>()) as List<Map<String, Any>>
            LogUtil.i("flutter 调用 fetchCommonParams,  uploadingRecordings: $uploadingRecordings")

            val handler = CoroutineExceptionHandler { context, exception ->
              LogUtil.i("flutter 调用 fetchCommonParams,  coroutineScope: ${exception.message}")
              result.error("INIT_ERROR", "fetchCommonParams failure: ${exception.message}", null)
            }
            coroutineScope.launch(handler+ Dispatchers.IO) {
              AliSpeechUploadHelper.uploadLocalFileAndMeeting(context, baseUrl, headerMap, uploadingRecordings,uploadStatusStreamHandler)
              result.success(true)
            }
          }catch (e: Exception){
            result.error("INIT_ERROR", "Failed to fetchCommonParams: ${e.message}", null)
          }
        }

        "initSpeechRecognition" -> {
          try {
            val params = call.argument<Map<String, Any>>("params")
            LogUtil.i("flutter 调用 initSpeechRecognition,  params: $params")
            val cookie = params?.getOrDefault("cookie","").toString() ?: ""
            val jwtoken = params?.getOrDefault("jwtoken","").toString() ?: ""
            val baseUrl = params?.getOrDefault("baseUrl",Api.URL_TEST).toString() ?: Api.URL_TEST
            val headerMap = mutableMapOf("cookie" to cookie, "jwtoken" to jwtoken)
            val handler = CoroutineExceptionHandler { context, exception ->
              LogUtil.i("flutter 调用 initSpeechRecognition,  coroutineScope: ${exception.message}")
              result.error("INIT_ERROR", "initSpeechRecognition failure: ${exception.message}", null)
            }
            coroutineScope.launch(handler) {
              LogUtil.i("initSpeechRecognition,  aliyun_nls_getToken")
              val token = request{ Api.getService(baseUrl).getAliyunNlsToken(headerMap) }?.token
              if (token.isNullOrBlank()){
                result.error("INIT_ERROR", "initSpeechRecognition failure: token isNullOrBlank", null)
                return@launch
              }
              AliSpeechHelper.setToken(token)
              AliSpeechHelper.init(context)
              result.success(true)
            }
          } catch (e: Exception) {
            result.error("INIT_ERROR", "Failed to initialize speech recognition: ${e.message}", null)
          }
        }
        "startSpeechRecognition" -> {
          try {
            coroutineScope.launch{
              val params = call.argument<Map<String, Any>>("params")
              LogUtil.i("flutter 调用 startSpeechRecognition,  params: $params")
              startSpeechRecognition(result, params)
            }
          } catch (e: Exception) {
            result.error("START_SPEECH_RECOGNITION_ERROR", "Failed to start speech recognition: ${e.message}", null)
          }
        }
        "stopSpeechRecognition" -> {
          try {
            val isCancel = call.argument<Boolean>("isCancel") ?: false
            LogUtil.e("flutter 调用 stopSpeechRecognition isCancel: $isCancel")
            AliSpeechHelper.stopDialog(isCancel, true)
            // 停止收集语音识别结果
            speechResultHandler?.stopCollecting()
            // 停止收集对话内容
            dialogContentStreamHandler?.stopCollecting()
//            speechJob?.cancel()
//            speechJob = null
            result.success(true)
          } catch (e: Exception) {
            result.error("STOP_ERROR", "Failed to stop speech recognition: ${e.message}", null)
          }
        }
        "pauseSpeechRecognition" -> {
          try {
            LogUtil.e("flutter 调用 pauseSpeechRecognition ")
            AliSpeechHelper.pause()
            result.success(true)
          } catch (e: Exception) {
            result.error("PAUSE_ERROR", "Failed to pause speech recognition: ${e.message}", null)
          }
        }
        "resumeSpeechRecognition" -> {
          try {
            LogUtil.e("flutter 调用 resumeSpeechRecognition ")
            AliSpeechHelper.resume()
            result.success(true)
          } catch (e: Exception) {
            result.error("RESUME_ERROR", "Failed to resume speech recognition: ${e.message}", null)
          }
        }
        "releaseSpeechRecognition" -> {
          try {
            LogUtil.e("flutter 调用 releaseSpeechRecognition ")
            AliSpeechHelper.release()
            result.success(true)
          } catch (e: Exception) {
            result.error("RELEASE_ERROR", "Failed to release speech recognition: ${e.message}", null)
          }
        }
        "uploadAndSubmitMeeting" -> {
          try {
            coroutineScope.launch {
              // 获取参数
              val params = call.argument<Map<String, Any>>("params")
              invokeUploadAndSubmitMeeting(result, params)
            }
          } catch (e: Exception) {
            result.error("UPLOAD_ERROR", "Failed to upload file: ${e.message}", null)
          }
        }
        else -> {
          result.notImplemented()
        }
      }
    }
    
    // 设置语音识别结果通道
    speechResultHandler = SpeechResultHandler()
    speechRecognitionResultChannel = EventChannel(flutterPluginBinding.binaryMessenger, "com.gankao.conference_assistant_plugin/speech_recognition_result")
    speechRecognitionResultChannel.setStreamHandler(speechResultHandler)

  }

  override fun onMethodCall(@NonNull call: MethodCall, @NonNull result: Result) {
    when (call.method) {
      "getPlatformVersion" -> {
        result.success("Android ${android.os.Build.VERSION.RELEASE}")
      }
      "startRecording" -> {
        try {
          val format = call.argument<String>("format") ?: "mp3"
          val path = call.argument<String>("path")
          
          if (path == null) {
            result.error("INVALID_ARGUMENT", "Recording path is required", null)
            return
          }
          
          // Initialize AudioRecordUtil if not already initialized
          if (audioRecordUtil == null) {
            audioRecordUtil = AudioRecordUtil()
            
            // Set up audio data listener
            audioRecordUtil?.setOnRecordListener(object : AudioRecordUtil.OnRecordListener {
              override fun recordByte(audioData: ByteArray, readSize: Int) {
                // Send audio data to Flutter through event channel
                audioStreamHandler?.sendAudioData(audioData, readSize)
              }
            })
            
            // Set up recording completion listener
            coroutineScope.launch {
              audioRecordUtil?.audioRecordStopShareFlow?.collect { filePath ->
                LogUtil.e("android 录音完成 audioRecordStopShareFlow: ${filePath} ")
                recordingCompletionHandler?.sendRecordingCompletion(filePath)
              }
            }
            
            // 设置音量监控
            coroutineScope.launch {
              audioRecordUtil?.audioVolumeStateFlow?.collect { volume ->
                //打印日志
                volumeStreamHandler?.sendVolumeData(volume)
              }
            }
          }
          
          // Start recording based on format
          when (format.lowercase()) {
            "mp3" -> audioRecordUtil?.startRecordToMP3(path)
            "wav" -> audioRecordUtil?.startRecordToWav(path)
            "aac" -> audioRecordUtil?.startRecordToAAC(path)
            else -> {
              // Default to MP3
              audioRecordUtil?.startRecordToMP3(path)
            }
          }
          
          result.success(true)
        } catch (e: Exception) {
          result.error("RECORDING_ERROR", "Failed to start recording: ${e.message}", null)
        }
      }
      "stopRecording" -> {
        try {
          audioRecordUtil?.stopRecord()
          result.success(true)
        } catch (e: Exception) {
          result.error("RECORDING_ERROR", "Failed to stop recording: ${e.message}", null)
        }
      }
      "setVolumeMonitoring" -> {
        try {
          val enabled = call.argument<Boolean>("enabled") ?: false
          audioRecordUtil?.isOpenVolumeDb = enabled
          
          // 设置音量监听
          if (enabled) {
            // 启动协程监听音量状态流并发送到Flutter
            coroutineScope.launch {
              audioRecordUtil?.audioVolumeStateFlow?.collect { volume ->
                volumeStreamHandler?.sendVolumeData(volume)
              }
            }
          }
          
          result.success(true)
        } catch (e: Exception) {
          result.error("RECORDING_ERROR", "Failed to set volume monitoring: ${e.message}", null)
        }
      }
      "getAudioVolume" -> {
        try {
          val volume = audioRecordUtil?.audioVolumeStateFlow?.value ?: 0.0
          result.success(volume)
        } catch (e: Exception) {
          result.error("RECORDING_ERROR", "Failed to get audio volume: ${e.message}", null)
        }
      }
      "addToMediaStore" -> {
        val filePath = call.argument<String>("filePath")
        if (filePath == null) {
          result.error("INVALID_ARGUMENT", "File path is required", null)
          return
        }
        
        try {
          addToMediaStore(filePath)
          result.success(true)
        } catch (e: Exception) {
          LogUtil.e("添加文件到媒体库出错: ${e.message}")
          result.error("MEDIA_STORE_ERROR", "Failed to add file to media store: ${e.message}", null)
        }
      }
      "openDirectoryExplorer" -> {
        val path = call.argument<String>("path")
        if (path == null) {
          result.error("INVALID_ARGUMENT", "Path is required", null)
          return
        }
        
        try {
          openDirectoryExplorer(path, result)
        } catch (e: Exception) {
          result.error("DIRECTORY_EXPLORER_ERROR", "Failed to open directory explorer: ${e.message}", null)
        }
      }
      else -> {
        result.notImplemented()
      }
    }
  }

  override fun onDetachedFromEngine(binding: FlutterPlugin.FlutterPluginBinding) {
    channel.setMethodCallHandler(null)
    audioEventChannel.setStreamHandler(null)
    recordingCompletionEventChannel.setStreamHandler(null)
    volumeEventChannel.setStreamHandler(null)
    audioRecordUtil?.release()
    audioRecordUtil = null
//    coroutineScope.launch {
//      // Cancel all coroutines
//      coroutineScope.coroutineContext[Job]?.cancel()
//    }
    speechRecognitionChannel.setMethodCallHandler(null)
    speechRecognitionResultChannel.setStreamHandler(null)
    speechResultHandler = null
    audioUrlEventChannel.setStreamHandler(null)
    audioUrlStreamHandler = null
    recordStateEventChannel.setStreamHandler(null)
    recordStateStreamHandler = null
    dialogContentEventChannel.setStreamHandler(null)
    dialogContentStreamHandler = null
    audioDurationEventChannel.setStreamHandler(null)
    audioDurationStreamHandler = null
    uploadStatusEventChannel.setStreamHandler(null)
    uploadStatusStreamHandler = null
    uploadProgressEventChannel.setStreamHandler(null)
    uploadProgressStreamHandler = null
  }

  // 添加这个方法将文件添加到MediaStore
  private fun addToMediaStore(filePath: String) {
    val file = File(filePath)
    if (!file.exists()) {
      throw Exception("File does not exist: $filePath")
    }
    
    // MediaStore添加文件的代码
    val values = ContentValues().apply {
      put(MediaStore.Audio.Media.DISPLAY_NAME, file.name)
      put(MediaStore.Audio.Media.MIME_TYPE, "audio/mpeg")
      put(MediaStore.Audio.Media.DATE_ADDED, System.currentTimeMillis() / 1000)
      put(MediaStore.Audio.Media.SIZE, file.length())
      if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
        put(MediaStore.Audio.Media.IS_PENDING, 1)
      }
    }
    
    val resolver = context.contentResolver
    val uri = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
      val collection: Uri = MediaStore.Audio.Media.getContentUri(MediaStore.VOLUME_EXTERNAL_PRIMARY)
      resolver.insert(collection, values)
    } else {
      val collection: Uri = MediaStore.Audio.Media.EXTERNAL_CONTENT_URI
      resolver.insert(collection, values)
    } ?: throw Exception("Failed to create new MediaStore record")
    
    // 将文件复制到MediaStore
    try {
      val outputStream = resolver.openOutputStream(uri)
      if (outputStream != null) {
        val inputStream = file.inputStream()
        inputStream.use { input ->
          outputStream.use { output ->
            input.copyTo(output)
          }
        }
      } else {
        throw Exception("Failed to open output stream for MediaStore")
      }
      
      // 完成后，更新IS_PENDING标志
      if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
        values.clear()
        values.put(MediaStore.Audio.Media.IS_PENDING, 0)
        resolver.update(uri, values, null, null)
      }
      
      LogUtil.i("文件已成功添加到媒体库: $filePath")
    } catch (e: Exception) {
      // 如果出错，删除部分创建的记录
      resolver.delete(uri, null, null)
      throw e
    }
  }

  // 打开文件夹的实现
  private fun openFolder(folderPath: String, result: Result) {
    try {
      val folder = File(folderPath)
      if (!folder.exists() || !folder.isDirectory) {
        result.error("INVALID_FOLDER", "The specified path does not exist or is not a directory", null)
        return
      }
      
      val intent = Intent(Intent.ACTION_VIEW)
      val uri = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
        // Android 7.0及以上，使用FileProvider
        val authority = "${context.packageName}.fileprovider"
        androidx.core.content.FileProvider.getUriForFile(context, authority, folder)
      } else {
        // Android 7.0以下
        Uri.fromFile(folder)
      }
      
      intent.setDataAndType(uri, "resource/folder")
      intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_GRANT_READ_URI_PERMISSION
      
      try {
        context.startActivity(intent)
        result.success(true)
      } catch (e: Exception) {
        // 如果默认的方式无法打开，尝试使用文件管理器
        val fileManagerIntent = Intent(Intent.ACTION_VIEW)
        fileManagerIntent.setDataAndType(Uri.parse(folderPath), "*/*")
        fileManagerIntent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
        
        try {
          context.startActivity(fileManagerIntent)
          result.success(true)
        } catch (e2: Exception) {
          result.error("ACTIVITY_NOT_FOUND", "No activity found to handle folder opening", null)
        }
      }
    } catch (e: Exception) {
      result.error("FOLDER_OPEN_ERROR", "Failed to open folder: ${e.message}", null)
    }
  }

  // 打开目录浏览器
  private fun openDirectoryExplorer(directoryPath: String, result: Result) {
    try {
      val directory = File(directoryPath)
      if (!directory.exists() || !directory.isDirectory) {
        result.error("INVALID_DIRECTORY", "The specified path does not exist or is not a directory", null)
        return
      }
      
      LogUtil.i("尝试打开文件夹: $directoryPath")
      
      // 尝试获取一个文件来测试这个文件夹下是否有文件
      val testFile = directory.listFiles()?.firstOrNull()
      
      // 方法1: 尝试使用ACTION_GET_CONTENT加上初始路径直接打开到具体文件夹
      try {
        val documentIntent = Intent(Intent.ACTION_OPEN_DOCUMENT_TREE)
        documentIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)

        // 尝试设置初始URI (Android 8.0+支持)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
          val path = directoryPath.replaceFirst("/storage/emulated/0/", "primary:")
            .replaceFirst("/storage/", "")
            .replace("/", "%2F")
          val initialUri = Uri.parse("content://com.android.externalstorage.documents/document/$path")
          documentIntent.putExtra("android.provider.extra.INITIAL_URI", initialUri)
        }

        context.startActivity(documentIntent)
        result.success(true)
        return
      } catch (e: Exception) {
        LogUtil.e("使用ACTION_OPEN_DOCUMENT_TREE打开文件夹失败: ${e.message}")
      }

      // 方法5: 通用文件管理器
      try {
        val intent = Intent(Intent.ACTION_VIEW)
        intent.setDataAndType(Uri.parse(directoryPath), "*/*")
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK

        context.startActivity(intent)
        result.success(true)
        return
      } catch (e: Exception) {
        LogUtil.e("使用通用方法打开文件夹失败: ${e.message}")
      }
      
      // 方法6: MediaStore方式 (适用于Android 10+)
      if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
        try {
          val contentUri = MediaStore.Files.getContentUri(MediaStore.VOLUME_EXTERNAL)
          val intent = Intent(Intent.ACTION_VIEW, contentUri)
          intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK

          context.startActivity(intent)
          result.success(true)
          return
        } catch (e: Exception) {
          LogUtil.e("使用MediaStore打开文件夹失败: ${e.message}")
        }
      }
      
      // 如果所有方法都失败，返回错误
      result.error("NO_FILE_MANAGER", "无法直接打开指定文件夹，用户需要手动导航", null)
    } catch (e: Exception) {
      result.error("DIRECTORY_EXPLORER_ERROR", "打开文件夹浏览器失败: ${e.message}", null)
    }
  }

  private suspend fun startSpeechRecognition(result: Result, params: Map<String, Any>?){
    try {
      LogUtil.e("flutter 调用 startSpeechRecognition,  params: $params")

      val audioSourceType = params?.getOrDefault("audioSourceType",1).toString().toIntOrNull()?:1
      val uuid = params?.getOrDefault("uuid",1).toString()
      // 设置录音文件路径
      // 获取系统下载目录路径

      val downloadDir = if(hasManageExternalStoragePermission()){
        Environment.getExternalStorageDirectory().absolutePath +"/cutflow_audio"
      }else {
        Environment.getExternalStoragePublicDirectory("Recordings").absolutePath+"/cutflow_audio"
      }
//      val rootPath = context?.getExternalFilesDir(null)?.absolutePath?:Environment.getExternalStorageDirectory().absolutePath
//      val downloadDir =  rootPath+"/curflow_audio"
      // 设置录音文件路径为下载目录下的文件
      val recordPath = downloadDir+"/${uuid}_${System.currentTimeMillis()}.mp3"
      // recordPath = context.filesDir.path+"/${uuid}_${System.currentTimeMillis()}.mp3"
      // 创建文件夹
      LogUtil.e("startSpeechRecognition 录音保存地址 downloadDir: $downloadDir")
      val file = File(downloadDir)
      if (!file.exists()){
        val created = file.mkdirs()
        LogUtil.e("startSpeechRecognition 创建文件夹,  created: $created")
      }
      speechJob?.cancel()
      speechJob = null
      // 1. 直接启动语音识别
      if (ActivityCompat.checkSelfPermission(
          context,
          Manifest.permission.RECORD_AUDIO
        ) != PackageManager.PERMISSION_GRANTED
      ) {
        result.error("RECOGNITION_ERROR", "启动语音识别失败:请开启录音权限", null)
        return
      }

      if (audioSourceType != 2){
        // 3. 开启前台服务
        if (Settings.canDrawOverlays(context) || true) {
          val intent = Intent(context, FloatingWindowService::class.java)
          intent.putExtra("audioSourceType",audioSourceType)
          if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            context.startForegroundService(intent)
          } else {
            context.startService(intent)
          }
          LogUtil.d(TAG, "前台服务已启动")
        } else {
          LogUtil.e(TAG, "未获得悬浮窗权限，无法启动前台服务")
        }
      }

    // 2. 请求录音权限
      if (audioSourceType == 2){
        val activity = this.activity
        if (activity == null){
          result.error("RECOGNITION_ERROR", "activity is null:请退出重试", null)
          return
        }
        LogUtil.e("startForeground===========requestScreenCapture  ${System.currentTimeMillis()}")
        val r = AliSpeechHelper.requestScreenCapture(activity)
        LogUtil.e("startSpeechRecognition requestScreenCapture result:$r")
        if (!r){
          result.error("RECOGNITION_ERROR", "请求录屏权限失败", null)
          return
        }
      }

      // 2. 获取服务实例设置通道
      floatingWindowService = FloatingWindowService.getInstance()
      floatingWindowService?.setMethodChannel(floatingWindowChannel)
      LogUtil.d(TAG, "startDialog recordPath: $recordPath  audioSourceType:$audioSourceType")
      val ret = AliSpeechHelper.startDialog(activity, recordPath ?: "", audioSourceType, false)
      if (ret) {
        LogUtil.d(TAG, "语音识别启动成功")
        LogUtil.e("floatingWindowService stopService=====11111===$floatingWindowService")
        speechJob?.cancel()
        speechJob = null
        // 最大180分钟
        val maxTime = 1
        val watcher = DebouncedMessageWatcher(context)
        speechJob = coroutineScope.launch {
          // 启动协程监听音频能量值
          launch {
            AliSpeechHelper.audioRMSVolumeSharedFlow.collect { volume ->
              audioRMSVolumeStreamHandler?.sendVolumeData(volume.toDouble())
            }
          }

          launch {
            var message = ""
            AliSpeechHelper.contentSharedFlow.collect { content ->
//            val resultMap = HashMap<String, Any?>()
//            resultMap["type"] = content.type
//            resultMap["curTaskId"] = content.curTaskId
//            resultMap["result"] = content.result
//            resultMap["resultCode"] = content.resultCode
//            resultMap["allResponse"] = content.allResponse

              if (!content.result.isBlank()){
                watcher.onMessageReceived()
              }
              val result = message+"\n"+content.result
              if (floatingWindowService != null) {
                floatingWindowService?.updateText(result)
              } else {
                FloatingWindowService.updateTextStatic(result)
              }
              if (content.type == "EVENT_SENTENCE_END"){
                message+= "\n${content.result}"
              }
            }
          }
//          launch {
//            AliSpeechHelper.audioRecordStopStateFlow.collect {
//              if (it.path.isBlank()){
//                return@collect
//              }
//              val url = AliSpeechUploadHelper.getTokenAndUploadFileToAliSpeech(it)
//              LogUtil.e(TAG, "getTokenAndUploadFileToAliSpeech url: $url")
//              // 发送音频URL到Flutter
//              if (!url.isNullOrBlank()) {
//                audioUrlStreamHandler?.sendAudioUrl(url)
//              }
//            }
//          }
          launch {
            AliSpeechHelper.recordStateFlow.collect {
//              const val PREPARE = "PREPARE"
//              const val RECORDING = "RECORDING"
//              const val PAUSED = "PAUSED"
//              const val STOPPED = "STOPPED"
//              const val ERROR = "ERROR"
              recordStateStreamHandler?.sendRecordState(it)
              if (it == RECORDING){
                context?.sendRecording(1)
              }else if(it == PAUSED){
                context?.sendRecording(2)
              }else if (it == STOPPED || it == ERROR){
                context?.sendRecording(3)
                FloatingWindowService.getInstance()?.stopService()
              }

              if (it == RECORDING){
                watcher.start()
              }else{
                watcher.stop()
              }
            }
          }
          
          launch {
            AliSpeechHelper.audioDurationStateFlow.collect { duration ->
              audioDurationStreamHandler?.sendAudioDuration(duration)
//              if (AliSpeechHelper.isBackground && duration == ((maxTime-0.5)*60).toInt()){
////                    剩10分钟达录音上限，将自动停止
////                    已录2小时50分钟，点此延长 1 小时
////                FloatingWindowService.getInstance()?.notify1(NOTIFICATION_ID_1)
//                LogUtil.e("FloatingWindowService", "剩10分钟达录音上限，将自动结束")
//              } else if (AliSpeechHelper.isBackground && duration == (maxTime)*60){
//                // 到达3小时上限自动结束
//                LogUtil.e("FloatingWindowService", "自动结束")
//                AliSpeechHelper.stopDialog()
//                speechResultHandler?.stopCollecting()
//                // 停止收集对话内容
//                dialogContentStreamHandler?.stopCollecting()
//              }
            }
          }
        }

        // 启动协程监听语音识别结果
        speechResultHandler?.startCollecting()
        
        // 启动对话内容收集
        dialogContentStreamHandler?.startCollecting()
        
        result.success(true)
      } else {
        LogUtil.e(TAG, "语音识别启动失败")
        result.error("RECOGNITION_ERROR", "启动语音识别失败", null)
      }
    } catch (e: Exception) {
      LogUtil.e(TAG, "启动语音识别时发生异常: ${e.message}")
      result.error("RECOGNITION_ERROR", "启动语音识别异常: ${e.message}", null)
    }
  }


  private suspend fun invokeUploadAndSubmitMeeting(result: Result, params: Map<String, Any>?){
    var name = params?.getOrDefault("name","").toString()
    if (name.isNullOrBlank()){
      name = TimeUtils.formatTimestampWithThreeTen()
    }
    val type = params?.getOrDefault("type","1").toString() ?:"1"
    val uuid = params?.getOrDefault("uuid","").toString() ?: ""
    val cookie = params?.getOrDefault("cookie","").toString() ?: ""
    val jwtoken = params?.getOrDefault("jwtoken","").toString() ?: ""
    val filePath = params?.getOrDefault("filePath","").toString() ?: ""
    val isEnd = params?.getOrDefault("isEnd",false) as? Boolean ?: false
    val baseUrl = params?.getOrDefault("baseUrl",Api.URL_TEST).toString() ?: Api.URL_TEST
    val headerMap = mutableMapOf("cookie" to cookie, "jwtoken" to jwtoken)
    LogUtil.e("flutter 调用 uploadAndSubmitMeeting,  params: $params")
    if (uuid.isNullOrBlank() || uuid == "null"){
      return
    }

    val formatName = name.replace(Regex("[^a-zA-Z0-9_.]"),"") //name.replace(Regex("[\u4e00-\u9fa5]"), "").replace(" ","")
    val uploadName = if (formatName.contains(".")){
      val dotIndex = formatName.lastIndexOf('.')
      Uri.encode("${formatName.substring(0, dotIndex)}_${System.currentTimeMillis()}${formatName.substring(dotIndex)}")
//      "aaa.mp3"
    }else{
      Uri.encode("${formatName}_${System.currentTimeMillis()}")
//    "bbbb.mp3"
    }
    val audioSourceType = params?.getOrDefault("audioSourceType",1).toString().toIntOrNull()?:1
    if (audioSourceType == 3){
      if (filePath.isNullOrBlank()){
        result.error("UPLOAD_ERROR", "invokeUploadAndSubmitMeeting: filePath is isNullOrBlank", null)
        return
      }
      val url = AliSpeechUploadHelper.getTokenAndUploadFileToAliSpeech(context, baseUrl, record = RecordInfoModel(path = filePath),
        uploadName,headerMap, onProgress = {
          // 发送上传进度到Flutter
          val progressMap = mutableMapOf<String, Any>()
          progressMap["upload_type"] = "upload_audio"
          
          progressMap["progress"] = (it * 100).toInt() // 转换为百分比
          uploadProgressStreamHandler?.sendUploadProgress(progressMap)
        })
      if (url.isNullOrBlank()){
        LogUtil.e("getTokenAndUploadFileToAliSpeech: url is null or blank")
        result.error("UPLOAD_ERROR", "getTokenAndUploadFileToAliSpeech: url is isNullOrBlank", null)
        return
      }
      LogUtil.e(TAG, "getTokenAndUploadFileToAliSpeech url: $url")
      LogUtil.e("uploadFiles success  content: ")
      val body = mutableMapOf<String, Any>()
      body["name"] = name
      body["uuid"] = uuid
      body["type"] = type
      body["file_url"] = url
      val r = request { Api.getService(baseUrl).uploadRecordingInfo(body, headerMap) }
      LogUtil.e("uploadRecordingInfo succes : $r  ${body}")
      // 发送上传完成状态
      val completionMap = mutableMapOf<String, Any>()
      completionMap["taskId"] = uuid
      completionMap["progress"] = 100
      completionMap["fileName"] = uploadName
      completionMap["status"] = "completed"
      uploadProgressStreamHandler?.sendUploadProgress(completionMap)
      
      uploadStatusStreamHandler?.sendUploadStatus(mutableMapOf<String, Any>().apply {
        this["upload_finish"] = 1
        this["refresh_list"] = 1
      })
      result.success("success")
      return
    }else{
      val map = mutableMapOf<String, Any>()
      map["type"] = type
      map["name"] = name
      map["uuid"] = uuid
      LogUtil.e("uploadAndSubmitMeeting,  getAllRecordings list aaaaaaa map: $map")
      MMKVHelper.saveRecording(uuid, map)
    }
    LogUtil.e("uploadAndSubmitMeeting,  audioSourceType: $audioSourceType")
    // 启动协程执行上传任务
    result.success("success")
    if (!isEnd){
      return
    }
    coroutineScope.launch {
      try {
        val record = AliSpeechHelper.audioRecordStopStateFlow.first{ it.path.isNotEmpty() }
        // 这里可以调用你的上传文件的方法，例如：
          LogUtil.e("uploadAndSubmitMeeting audioRecordStopStateFlow: $record")
          val content = AliSpeechHelper.dialogContentStateFlow.value
          if (record.path == "cancel"){
            return@launch
          }
          //uui
//                  "name": "录音文件 test 13:25",
//                  "type": 2,
//                  "file_url": "https://asr-file-meeting.oss-cn-hangzhou.aliyuncs.com/recordingSize.mp3",
//                  "file_duration": 592,
//                  "tapescript": "我是测试的录音稿"
          speechJob?.cancel()
          speechJob = null
          val url = AliSpeechUploadHelper.getTokenAndUploadFileToAliSpeech(context ,baseUrl, record = record, uploadName, headerMap, onProgress = {
            // 发送上传进度到Flutter
            val progressMap = mutableMapOf<String, Any>()
            progressMap["taskId"] = uuid
            progressMap["progress"] = (it * 100).toInt() // 转换为百分比
            progressMap["fileName"] = uploadName
            progressMap["status"] = "uploading"
            uploadProgressStreamHandler?.sendUploadProgress(progressMap)
          })
          if (url.isNullOrBlank()){
            LogUtil.e("getTokenAndUploadFileToAliSpeech: url is null or blank")
//            result.error("UPLOAD_ERROR", "getTokenAndUploadFileToAliSpeech: url is isNullOrBlank", null)
            return@launch
          }
          LogUtil.e(TAG, "getTokenAndUploadFileToAliSpeech url: $url")
          val duration = record.duration

          LogUtil.e("uploadFiles success  content: ${content}")
          val body = mutableMapOf<String, Any>()
          body["name"] = name
          body["uuid"] = uuid
          body["type"] = type
          body["file_url"] = url
          body["file_duration"] = duration
          body["tapescript"] = content.second
          val result = request { Api.getService(baseUrl).uploadRecordingInfo(body, headerMap) }
          if (result != null){
            LogUtil.e("uploadRecordingInfo success : 上传成功，删除本地文件 ${record.path}")
            val file = File(record.path)
            runCatching {
              file.delete()
              MMKVHelper.removeRecording(uuid)
            }
          }
          LogUtil.e("uploadRecordingInfo success : $result  ${body}")
          // 发送上传完成状态
          val completionMap = mutableMapOf<String, Any>()
          completionMap["taskId"] = uuid
          completionMap["progress"] = 100
          completionMap["fileName"] = uploadName
          completionMap["status"] = "completed"
          uploadProgressStreamHandler?.sendUploadProgress(completionMap)
          
          withContext(Dispatchers.IO){
            delay(3000)
          }
          uploadStatusStreamHandler?.sendUploadStatus(mutableMapOf<String, Any>().apply {
            this["upload_finish"] = 1
            this["refresh_list"] = 1
          })
        // 在主线程返回结果
      } catch (e: Exception) {
        // 在主线程返回错误
      }
    }
  }

  override fun onAttachedToActivity(binding: ActivityPluginBinding) {
    activity = binding.activity
    this.binding = binding
    LogUtil.e("ConferenceAssistantPlugin", "onAttachedToActivity:$activity")
    binding.addActivityResultListener(AliSpeechHelper.getResult(binding.activity))
  }

  override fun onDetachedFromActivity() {
    activity = null
    LogUtil.e("ConferenceAssistantPlugin", "onDetachedFromActivity")
    AliSpeechHelper.activityResultListener?.let {
      binding?.removeActivityResultListener(it)
    }
    AliSpeechHelper.activityResultListener = null
    this.binding = null
  }

  override fun onReattachedToActivityForConfigChanges(binding: ActivityPluginBinding) {
    activity = binding.activity
    this.binding = binding
    LogUtil.e("ConferenceAssistantPlugin", "onReattachedToActivityForConfigChanges:$activity")
    binding.addActivityResultListener(AliSpeechHelper.getResult(binding.activity))
  }

  override fun onDetachedFromActivityForConfigChanges() {
    activity = null
    LogUtil.e("ConferenceAssistantPlugin", "onDetachedFromActivityForConfigChanges")
    AliSpeechHelper.activityResultListener?.let {
      binding?.removeActivityResultListener(it)
    }
    AliSpeechHelper.activityResultListener = null
    this.binding = null
  }
}

/**
 * Stream handler for sending audio data to Flutter
 */
class AudioStreamHandler : EventChannel.StreamHandler {
  private var eventSink: EventChannel.EventSink? = null
  private val mainHandler = Handler(Looper.getMainLooper())

  override fun onListen(arguments: Any?, events: EventChannel.EventSink?) {
    eventSink = events
  }

  override fun onCancel(arguments: Any?) {
    eventSink = null
  }

  fun sendAudioData(audioData: ByteArray, readSize: Int) {
    val data = HashMap<String, Any>()
    data["audioData"] = audioData
    data["readSize"] = readSize
    
    // 使用主线程发送数据
    mainHandler.post {
      eventSink?.success(data)
    }
  }
}

/**
 * Stream handler for sending recording completion events to Flutter
 */
class RecordingCompletionHandler : EventChannel.StreamHandler {
  private var eventSink: EventChannel.EventSink? = null
  private val mainHandler = Handler(Looper.getMainLooper())

  override fun onListen(arguments: Any?, events: EventChannel.EventSink?) {
    eventSink = events
  }

  override fun onCancel(arguments: Any?) {
    eventSink = null
  }

  fun sendRecordingCompletion(filePath: String) {
    // 使用主线程发送数据
    mainHandler.post {
      eventSink?.success(filePath)
    }
  }
}

/**
 * Stream handler for sending volume data to Flutter
 */
class VolumeStreamHandler : EventChannel.StreamHandler {
  private var eventSink: EventChannel.EventSink? = null
  private val mainHandler = Handler(Looper.getMainLooper())

  override fun onListen(arguments: Any?, events: EventChannel.EventSink?) {
    eventSink = events
  }

  override fun onCancel(arguments: Any?) {
    eventSink = null
  }

  fun sendVolumeData(volume: Double) {
    // 使用主线程发送数据
    mainHandler.post {
      eventSink?.success(volume)
    }
  }
}

/**
 * Stream handler for sending speech recognition results to Flutter
 */
class SpeechResultHandler : EventChannel.StreamHandler {
  private var eventSink: EventChannel.EventSink? = null
  private val mainHandler = Handler(Looper.getMainLooper())
  private var job: Job? = null
  private val coroutineScope = CoroutineScope(Dispatchers.Main + Job())
  
  override fun onListen(arguments: Any?, events: EventChannel.EventSink?) {
    LogUtil.e("android SpeechResultHandler======== onListen: $events")
    eventSink = events
  }

  override fun onCancel(arguments: Any?) {
    LogUtil.e("android SpeechResultHandler======== onCancel: ")
    eventSink = null
    stopCollecting()
  }
  
  // 开始收集语音识别结果
  fun startCollecting() {
    // 先停止之前的收集
    stopCollecting()
    
    // 如果 eventSink 为空，尝试等待一小段时间再检查
    actuallyStartCollecting()
  }
  
  // 实际开始收集的方法
  private fun actuallyStartCollecting() {

    // 确保 job 为空
    job?.cancel()
    job = null
    LogUtil.e("SpeechResultHandler actuallyStartCollecting")
    // 启动新的收集
    job = coroutineScope.launch {
      AliSpeechHelper.contentSharedFlow.collect { content ->
        val resultMap = HashMap<String, Any?>()
        resultMap["type"] = content.type
        resultMap["curTaskId"] = content.curTaskId
        resultMap["result"] = content.result
        resultMap["resultCode"] = content.resultCode
        resultMap["allResponse"] = content.allResponse
        // 使用主线程发送数据
        mainHandler.post {
          if (eventSink != null) {
            LogUtil.e("识别到的结果contentSharedFlow 发送给flutter: eventSink：${eventSink}  resultMap ${resultMap}")
            eventSink?.success(resultMap)
          } else {
            LogUtil.e("无法发送识别结果，eventSink 为 null")
          }
        }
      }
    }
  }
  
  // 停止收集语音识别结果
  fun stopCollecting() {
    job?.cancel()
    job = null
  }
}

/**
 * Stream handler for sending audio RMS volume data to Flutter
 */
class AudioRMSVolumeStreamHandler : EventChannel.StreamHandler {
  private var eventSink: EventChannel.EventSink? = null
  private val mainHandler = Handler(Looper.getMainLooper())

  override fun onListen(arguments: Any?, events: EventChannel.EventSink?) {
    LogUtil.e("AudioRMSVolumeStreamHandler : onListen")
    eventSink = events
  }

  override fun onCancel(arguments: Any?) {
    LogUtil.e("AudioRMSVolumeStreamHandler : onCancel")
    eventSink = null
  }

  fun sendVolumeData(volume: Double) {
    // 使用主线程发送数据
    mainHandler.post {
      eventSink?.success(volume)
    }
  }
}

/**
 * Stream handler for sending audio URL to Flutter
 */
class AudioUrlStreamHandler : EventChannel.StreamHandler {
  private var eventSink: EventChannel.EventSink? = null
  private val mainHandler = Handler(Looper.getMainLooper())

  override fun onListen(arguments: Any?, events: EventChannel.EventSink?) {
    LogUtil.e("AudioUrlStreamHandler : onListen")
    eventSink = events
  }

  override fun onCancel(arguments: Any?) {
    LogUtil.e("AudioUrlStreamHandler : onCancel")
    eventSink = null
  }

  fun sendAudioUrl(url: String) {
    // 使用主线程发送数据
    mainHandler.post {
      if (eventSink != null) {
        LogUtil.e("发送音频URL到Flutter: $url")
        eventSink?.success(url)
      } else {
        LogUtil.e("无法发送音频URL，eventSink 为 null")
      }
    }
  }
}

/**
 * Stream handler for sending recording state to Flutter
 */
class RecordStateStreamHandler : EventChannel.StreamHandler {
  private var eventSink: EventChannel.EventSink? = null
  private val mainHandler = Handler(Looper.getMainLooper())

  override fun onListen(arguments: Any?, events: EventChannel.EventSink?) {
    LogUtil.e("RecordStateStreamHandler : onListen")
    eventSink = events
  }

  override fun onCancel(arguments: Any?) {
    LogUtil.e("RecordStateStreamHandler : onCancel")
    eventSink = null
  }

  fun sendRecordState(state: Any) {
    // 使用主线程发送数据
    mainHandler.post {
      if (eventSink != null) {
        LogUtil.e("发送录音状态到Flutter: $state")
        eventSink?.success(state.toString())
      } else {
        LogUtil.e("无法发送录音状态，eventSink 为 null")
      }
    }
  }
}

/**
 * Stream handler for sending dialog content to Flutter
 */
class DialogContentStreamHandler : EventChannel.StreamHandler {
  private var eventSink: EventChannel.EventSink? = null
  private val mainHandler = Handler(Looper.getMainLooper())
  private var job: Job? = null
  private val coroutineScope = CoroutineScope(Dispatchers.Main + Job())
  
  override fun onListen(arguments: Any?, events: EventChannel.EventSink?) {
    LogUtil.e("DialogContentStreamHandler : onListen")
    eventSink = events
  }

  override fun onCancel(arguments: Any?) {
    LogUtil.e("DialogContentStreamHandler : onCancel")
    eventSink = null
    stopCollecting()
  }
  
  fun startCollecting() {
    stopCollecting()
    actuallyStartCollecting()
  }
  
  private fun actuallyStartCollecting() {
    job?.cancel()
    job = null
    LogUtil.e("DialogContentStreamHandler actuallyStartCollecting")
    
    job = coroutineScope.launch {
      AliSpeechHelper.dialogContentStateFlow.collect { content ->
        val resultMap = HashMap<String, Any?>()
        resultMap["task_id"] = content.first
        resultMap["content"] = content.second
        
        mainHandler.post {
          if (eventSink != null) {
            LogUtil.e("对话内容发送给FlutterdialogContentStateFlow : eventSink: $eventSink, content: $content")
            eventSink?.success(resultMap)
          } else {
            LogUtil.e("无法发送对话内容，eventSink为null")
          }
        }
      }
    }
  }
  
  fun stopCollecting() {
    job?.cancel()
    job = null
  }
}

/**
 * Stream handler for audio duration
 */
class AudioDurationStreamHandler : EventChannel.StreamHandler {
  private var eventSink: EventChannel.EventSink? = null
  private val mainHandler = Handler(Looper.getMainLooper())
  
  override fun onListen(arguments: Any?, events: EventChannel.EventSink?) {
    LogUtil.e("android AudioDurationStreamHandler======== onListen: $events")
    eventSink = events

  }

  override fun onCancel(arguments: Any?) {
    LogUtil.e("android AudioDurationStreamHandler======== onCancel: ")
    eventSink = null

  }

  // 实际开始收集的方法
  fun sendAudioDuration(duration: Int) {
    mainHandler.post {
      if (eventSink != null) {
        LogUtil.e("当前音频持续时间: $duration 秒")
        eventSink?.success(duration)
      } else {
        LogUtil.e("无法发送音频持续时间，eventSink 为 null")
      }
    }
  }
}

/**
 * Stream handler for sending upload status map data to Flutter
 */
class UploadStatusStreamHandler : EventChannel.StreamHandler {
  private var eventSink: EventChannel.EventSink? = null
  private val mainHandler = Handler(Looper.getMainLooper())

  override fun onListen(arguments: Any?, events: EventChannel.EventSink?) {
    LogUtil.e("UploadStatusStreamHandler : onListen")
    eventSink = events
  }

  override fun onCancel(arguments: Any?) {
    LogUtil.e("UploadStatusStreamHandler : onCancel")
    eventSink = null
  }

  fun sendUploadStatus(statusMap: Map<String, Any>) {
    mainHandler.post {
      if (eventSink != null) {
        LogUtil.e("发送上传状态数据到Flutter: $statusMap")
        eventSink?.success(statusMap)
      } else {
        LogUtil.e("无法发送上传状态数据，eventSink 为 null")
      }
    }
  }
}

/**
 * Stream handler for sending upload progress data to Flutter
 */
class UploadProgressStreamHandler : EventChannel.StreamHandler {
  private var eventSink: EventChannel.EventSink? = null
  private val mainHandler = Handler(Looper.getMainLooper())

  override fun onListen(arguments: Any?, events: EventChannel.EventSink?) {
    LogUtil.e("UploadProgressStreamHandler : onListen")
    eventSink = events
  }

  override fun onCancel(arguments: Any?) {
    LogUtil.e("UploadProgressStreamHandler : onCancel")
    eventSink = null
  }

  fun sendUploadProgress(progressMap: Map<String, Any>) {
    mainHandler.post {
      if (eventSink != null) {
        LogUtil.e("发送上传进度数据到Flutter: $progressMap")
        eventSink?.success(progressMap)
      } else {
        LogUtil.e("无法发送上传进度数据，eventSink 为 null")
      }
    }
  }
}

