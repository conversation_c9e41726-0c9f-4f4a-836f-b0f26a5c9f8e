package com.gankao.conference_assistant_plugin.material

import android.R.attr.duration
import android.R.attr.path
import android.R.attr.type
import android.content.Context
import android.net.Uri
import com.alibaba.fastjson.JSONObject
import com.gankao.conference_assistant_plugin.UploadStatusStreamHandler
import com.gankao.conference_assistant_plugin.api.Api
import com.gankao.conference_assistant_plugin.api.Api.URL_TEST
import com.gankao.conference_assistant_plugin.utils.AudioUtils
import com.gankao.conference_assistant_plugin.utils.MMKVHelper
import com.gankao.conference_assistant_plugin.utils.sendNativeParams
import com.gankao.network.http.request
import com.gk.logcat.LogUtil
import com.gk.logcat.LogUtil.context
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileOutputStream
import kotlin.collections.set

object MaterialUploadHelper {

    suspend fun getTokenAndUploadFile(context: Context, path: String, name: String): String? {
        return withContext(Dispatchers.IO) {
            delay(10000)
            return@withContext "https://www.baidu.com/img/PCfb_5bf082d29588c07f842ccde3f97243ea.png"
        }
    }

}