package com.gankao.conference_assistant_plugin.utils

import android.content.Context
import com.alibaba.fastjson.JSONObject
import com.gk.logcat.LogUtil
import com.tencent.mmkv.MMKV




object MMKVHelper {

    private val mmkv by lazy {
        MMKV.defaultMMKV()
    }
    fun init(context: Context){
        MMKV.initialize(context);
    }

    fun saveRecording(key: String, value: Map<String, Any>) {
        val valueString = JSONObject(value).toJSONString()
        val list = getAllRecordings().toMutableSet()
        list.removeIf { it.contains(key) }
        list.add(valueString)
        LogUtil.i("MMKVHelper saveRecording :$list  key:$key")
        mmkv.encode("list_recording", list)
    }

    fun getRecording(key: String): Map<String, Any>? {
        val valueString = getAllRecordings().firstOrNull { it.contains(key) }
        LogUtil.i("MMKVHelper getRecording :$valueString  key:$key")
        return if (valueString != null) {
            JSONObject.parseObject(valueString, Map::class.java) as? Map<String, Any>
        } else {
            null
        }
    }

    fun getAllRecordings(): MutableSet<String>{
        LogUtil.i("MMKVHelper getAllRecordings :list_recording")
        return mmkv.decodeStringSet("list_recording", emptySet()) ?: mutableSetOf()
    }

    fun clearAllRecordings() {
        LogUtil.i("MMKVHelper clearAllRecordings :list_recording")
        mmkv.removeValueForKey("list_recording")
    }
    fun removeRecording(key: String) {
        val list = getAllRecordings().toMutableSet()
        list.removeIf { it.contains(key) }
        LogUtil.i("MMKVHelper removeRecording :$list  key:$key")
        mmkv.encode("list_recording", list)
        mmkv.remove(key)
    }

    fun saveRecordingDuration(key: String, duration: Int){
        mmkv.encode(key, duration)
    }

    fun getRecordingDuration(key: String): Int{
        return mmkv.decodeInt(key, 0)
    }
}