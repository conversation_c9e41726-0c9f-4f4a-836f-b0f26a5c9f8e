package com.gankao.conference_assistant_plugin.receiver

import android.app.ActivityManager
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.os.Build
import android.util.Log
import com.gankao.conference_assistant_plugin.utils.sendNativeParams
import com.gk.logcat.LogUtil

class NotificationClickReceiver : BroadcastReceiver() {
    override fun onReceive(context: Context, intent: Intent?) {
        context.sendNativeParams("/recording")
//        val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
//            val tasks = activityManager.appTasks
//            if (tasks.isNotEmpty()) {
//                tasks[0].moveToFront()
//                LogUtil.d("NotificationClick", "成功恢复 App 到前台")
//            } else {
//                LogUtil.w("NotificationClick", "未找到 App 任务，可能已被系统杀死")
//            }
//        }
    }
}