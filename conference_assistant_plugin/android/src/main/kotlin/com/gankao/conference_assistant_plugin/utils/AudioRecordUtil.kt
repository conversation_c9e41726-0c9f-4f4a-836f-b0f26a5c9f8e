package com.gankao.conference_assistant_plugin.utils
import android.annotation.SuppressLint
import android.media.AudioFormat
import android.media.AudioRecord
import android.media.MediaCodec
import android.media.MediaCodecInfo
import android.media.MediaFormat
import android.media.MediaRecorder
import com.gk.logcat.LogUtil
import com.gk.speex.jnibridge.SpeexJNIBridge
import com.naman14.androidlame.LameBuilder
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import kotlinx.coroutines.sync.Mutex
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.io.OutputStream
import kotlin.math.log10
import kotlin.math.sqrt
import kotlin.experimental.and

@SuppressLint("MissingPermission")
class AudioRecordUtil {
    private val mainScope = MainScope()
    private val bufferSizeInBytes by lazy {
        AudioRecord.getMinBufferSize(
            44100,
            AudioFormat.CHANNEL_IN_STEREO,
            AudioFormat.ENCODING_PCM_16BIT
        )
    }

    var isOpenVolumeDb = false
    private var audioRecord: AudioRecord? = null
    private val startStateFlow = MutableStateFlow(false)
    private var onRecordListener: OnRecordListener? = null
    private val _audioVolumeStateFlow = MutableStateFlow(0.0)
    val audioVolumeStateFlow = _audioVolumeStateFlow.asStateFlow()

    private val _audioRecordStopShareFlow = MutableSharedFlow<String>()
    val audioRecordStopShareFlow = _audioRecordStopShareFlow.asSharedFlow()


    fun setOnRecordListener(onRecordListener: OnRecordListener) {
        this.onRecordListener = onRecordListener
    }

    fun startRecord() {
        if (startStateFlow.value) return
        audioRecord = AudioRecord(
            MediaRecorder.AudioSource.MIC,
            44100,
            AudioFormat.CHANNEL_IN_STEREO,
            AudioFormat.ENCODING_PCM_16BIT,
            bufferSizeInBytes
        );
        mainScope.launch(Dispatchers.IO) {
            startStateFlow.value = true
            audioRecord?.startRecording()
            val audioData = ByteArray(bufferSizeInBytes)
            while (startStateFlow.value) {
                val readSize = audioRecord?.read(audioData, 0, bufferSizeInBytes) ?: 0
                if (isOpenVolumeDb){
                    val db = calculateDb(audioData)
                    _audioVolumeStateFlow.value = db
                }
                onRecordListener?.recordByte(audioData, readSize)
            }
            if (audioRecord != null) {
                audioRecord?.stop()
                audioRecord?.release()
                audioRecord = null
            }
            _audioRecordStopShareFlow.emit("")
        }
    }

    fun stopRecord() {
        startStateFlow.value = false
    }

    fun startRecordToWav(path: String, sampleRate: Int = 16000){
        if (startStateFlow.value) return

        val bufferSizeInBytes = AudioRecord.getMinBufferSize(
            sampleRate,
            AudioFormat.CHANNEL_IN_MONO,
            AudioFormat.ENCODING_PCM_16BIT
        ) * 2

        audioRecord = AudioRecord(
            MediaRecorder.AudioSource.MIC,
            sampleRate,
            AudioFormat.CHANNEL_IN_MONO,
            AudioFormat.ENCODING_PCM_16BIT,
            bufferSizeInBytes
        );

        val outputFile = File(path)
        mainScope.launch(Dispatchers.IO) {
            SpeexJNIBridge.destory()
            SpeexJNIBridge.init(bufferSizeInBytes, sampleRate)
            val outputStream = outputFile.outputStream()
            startStateFlow.value = true
            audioRecord?.startRecording()
            val audioData = ByteArray(bufferSizeInBytes)

            outputStream.use { fos ->
                var totalAudioLen = 0L
                writeEmptyWaveFileHeader(fos)
                while (startStateFlow.value) {
                    val readSize = audioRecord?.read(audioData, 0, bufferSizeInBytes) ?: 0
                    LogUtil.e("audioData:=========== ${audioData.size}")
                    SpeexJNIBridge.denoise(audioData)
                    if (isOpenVolumeDb){
                        val db = calculateDb(audioData)
                        _audioVolumeStateFlow.value = db
                    }
                    if (readSize > 0){
                        fos.write(audioData, 0, readSize)
                    }
                    totalAudioLen += readSize
                }
                writeWaveFileHeader(fos, totalAudioLen, totalAudioLen + 36, sampleRate, 16,
                    1, (sampleRate * 1 * 16 / 8).toLong()
                )
            }

            if (audioRecord != null) {
                audioRecord?.stop()
                audioRecord?.release()
                audioRecord = null
            }
            SpeexJNIBridge.destory();
            _audioRecordStopShareFlow.emit(path)
        }


    }



    private val mutex = Mutex()

    fun startRecordToAAC(path: String) {
        if (startStateFlow.value) return
        audioRecord = AudioRecord(
            MediaRecorder.AudioSource.MIC,
            44100,
            AudioFormat.CHANNEL_IN_STEREO,
            AudioFormat.ENCODING_PCM_16BIT,
            bufferSizeInBytes
        )

        val audioMediacodec = initMediacodec()
        val outputFile = File(path)
        val outputStream = FileOutputStream(outputFile)
        val audioBufferInfo = MediaCodec.BufferInfo()
        audioMediacodec.start()
        // 捕获协程异常
        startStateFlow.value = true

        val recordStopStateFlow = MutableStateFlow(false)
        val codecStopStateFlow = MutableStateFlow(false)

        val handler = CoroutineExceptionHandler { coroutineContext, throwable ->
            LogUtil.e("audioMediacodec  startRecordToAAC: ${mutex.isLocked} $throwable")
            if (mutex.isLocked){
                mutex.unlock()
            }
        }
        mainScope.launch(handler+Dispatchers.IO) {
            launch {
                recordStopStateFlow.combine(codecStopStateFlow){ record, codec->
                    record && codec
                }.stateIn(this, started = SharingStarted.WhileSubscribed(), false)
                    .collectLatest {
                        if (it){
                            _audioRecordStopShareFlow.emit(path)
                        }
                    }
            }
            launch(Dispatchers.IO) {
                audioRecord?.startRecording()
                val audioData = ByteArray(bufferSizeInBytes)
                var pts = 0L
                while (true) {
                    if (!startStateFlow.value) {
                        break
                    }
                    val readSize = audioRecord?.read(audioData, 0, bufferSizeInBytes) ?: 0
                    if (isOpenVolumeDb){
                        val db = calculateDb(audioData)
                        _audioVolumeStateFlow.value = db
                    }
                    onRecordListener?.recordByte(audioData, readSize)
                    pts += (1.0 * readSize / (44100 * 2 * 2) * 1000000).toLong()
//                    LogUtil.e("audioMediacodec  startRecordToAAC: pts:$pts  readSize:$readSize")
                    mutex.lock()
                    audioMediacodec.putPcmData(pts, audioData, readSize)
                    mutex.unlock()
                    delay(5)

                }
                if (audioRecord != null) {
                    audioRecord?.stop()
                    audioRecord?.release()
                    audioRecord = null
                }
                recordStopStateFlow.value = true
            }
            launch(Dispatchers.IO) {
                while (true) {
                    if (!startStateFlow.value) {
                        audioMediacodec.stop()
                        audioMediacodec.release()
                        outputStream.close()
                        break
                    }
                    mutex.lock()
                    // 4. 获取输出缓冲区
                    val outputBufferIndex = audioMediacodec.dequeueOutputBuffer(audioBufferInfo, 0)
                    if (outputBufferIndex >= 0) {
                        if (audioBufferInfo.flags and MediaCodec.BUFFER_FLAG_END_OF_STREAM != 0) {
//                            isEOS = true
                        }
                        val outputBuffer =
                            audioMediacodec.getOutputBuffer(outputBufferIndex) ?: continue
                        if (audioBufferInfo.flags and MediaCodec.BUFFER_FLAG_CODEC_CONFIG != 0) {
                            // 忽略 csd-0 配置数据
                            audioMediacodec.releaseOutputBuffer(outputBufferIndex, false)
                            mutex.unlock()
                            continue
                        }

                        outputBuffer.position(audioBufferInfo.offset)
                        outputBuffer.limit(audioBufferInfo.offset + audioBufferInfo.size)
                        val aacData = ByteArray(audioBufferInfo.size)
                        outputBuffer.get(aacData)
                        outputStream.write(addAdtsHeader(aacData, aacData.size + 7, 44100))
//                        outputStream.write(aacData)
                        audioMediacodec.releaseOutputBuffer(outputBufferIndex, false)
                    }
                    mutex.unlock()
                }
                codecStopStateFlow.value = true
            }
        }
    }



    fun startRecordToMP3(path: String){
        if (startStateFlow.value) return

        // 定义明确的采样率和通道数
        val sampleRate = 44100
        val channelConfig = AudioFormat.CHANNEL_IN_MONO // 使用单声道录制
        val channelCount = 1 // 单声道对应的通道数

        // 确保使用足够大的缓冲区
        val minBufferSize = AudioRecord.getMinBufferSize(
            sampleRate,
            channelConfig,
            AudioFormat.ENCODING_PCM_16BIT
        )
        val recordBufferSize = minBufferSize * 2

        try {
            audioRecord = AudioRecord(
                MediaRecorder.AudioSource.MIC,
                sampleRate,
                channelConfig,
                AudioFormat.ENCODING_PCM_16BIT,
                recordBufferSize
            )

            // 确保使用相同的采样率和通道数初始化LAME编码器
            val androidLame = LameBuilder()
                .setInSampleRate(sampleRate)
                .setOutSampleRate(sampleRate) // 确保输出采样率与输入相同
                .setOutChannels(channelCount)
                .setOutBitrate(128) // 设置合理的比特率
                .setQuality(5) // 设置质量 (0=最好/慢, 9=最差/快)
                .setVbrMode(LameBuilder.VbrMode.VBR_OFF) // 使用更高质量的VBR模式
                .setVbrQuality(4) // VBR质量设置 (0=最好/慢, 9=最差/快)
                .build()

            val outputFile = File(path)
            val handler = CoroutineExceptionHandler { _, throwable ->
                LogUtil.e("MP3 recording error: ${throwable.message}")
                throwable.printStackTrace()
            }

            mainScope.launch(handler + Dispatchers.IO) {
                try {
                    val outputStream = outputFile.outputStream()
                    startStateFlow.value = true
                    audioRecord?.startRecording()

                    // PCM缓冲区
                    val pcmBuffer = ShortArray(recordBufferSize / 2)  // 16位=2字节

                    // MP3缓冲区 (1.25倍PCM大小应该足够)
                    val mp3Buffer = ByteArray((7200 + recordBufferSize * 1.25).toInt())

                    // 为立体声录制创建空的右声道数组
                    val emptyRightChannel = if (channelCount == 2) ShortArray(pcmBuffer.size / 2) else shortArrayOf(0)

                    outputStream.use { fos ->
                        // 写入ID3v2标签
                        writeID3v2Tag(fos)

                        while (startStateFlow.value) {
                            val readSize = audioRecord?.read(pcmBuffer, 0, pcmBuffer.size) ?: 0
                            if (readSize <= 0) {
                                // 读取失败，等待一下再继续
                                delay(10)
                                continue
                            }

                            if (isOpenVolumeDb) {
                                val byteBuffer = ByteArray(readSize * 2)
                                // 将short数组转换为byte数组以计算分贝
                                for (i in 0 until readSize) {
                                    val sample = pcmBuffer[i]
                                    byteBuffer[i * 2] = (sample and 0xFF).toByte()
                                    byteBuffer[i * 2 + 1] = (sample.toInt() shr 8).toByte()
                                }
                                val db = calculateDb(byteBuffer)
                                _audioVolumeStateFlow.value = db
                            }
                            if (onRecordListener != null) {
                                // 将PCM数据(ShortArray)转换为ByteArray
                                val audioData = ByteArray(readSize * 2)
                                for (i in 0 until readSize) {
                                    audioData[i * 2] = (pcmBuffer[i] and 0xFF).toByte()
                                    audioData[i * 2 + 1] = (pcmBuffer[i].toInt() shr 8).toByte()
                                }
                                onRecordListener?.recordByte(audioData, audioData.size)
                            }
                            // 编码为MP3 - 安全处理单声道和立体声情况
                            val encodedSize = if (channelCount == 1) {
                                // 单声道编码 - 第二个参数传null
                                androidLame.encode(pcmBuffer, shortArrayOf(0), readSize, mp3Buffer)
                            } else {
                                // 立体声编码 - 需要分离左右声道
                                androidLame.encode(pcmBuffer, emptyRightChannel, readSize / 2, mp3Buffer)
                            }

                            if (encodedSize > 0) {
                                fos.write(mp3Buffer, 0, encodedSize)
                            }
                        }

                        // 完成编码，写入最后的MP3帧
                        var flushSize: Int
                        do {
                            flushSize = androidLame.flush(mp3Buffer)
                            if (flushSize > 0) {
                                fos.write(mp3Buffer, 0, flushSize)
                            }
                        } while (flushSize > 0)

                        // 写入ID3v1标签
                        writeID3v1Tag(fos)
                    }

                    // 关闭LAME编码器
                    androidLame.close()

                } catch (e: Exception) {
                    LogUtil.e("MP3 recording error: ${e.message}")
                    e.printStackTrace()
                } finally {
                    if (audioRecord != null) {
                        try {
                            audioRecord?.stop()
                            audioRecord?.release()
                        } catch (e: Exception) {
                            LogUtil.e("Error releasing AudioRecord: ${e.message}")
                        } finally {
                            audioRecord = null
                        }
                    }
                }
                _audioRecordStopShareFlow.emit(path)
            }
        } catch (e: Exception) {
            LogUtil.e("Failed to initialize AudioRecord: ${e.message}")
            e.printStackTrace()
        }
    }


    /**
     * 写入ID3v2标签到MP3文件
     */
    private fun writeID3v2Tag(outputStream: OutputStream) {
        try {
            // ID3v2标签头
            val header = ByteArray(10)
            header[0] = 'I'.code.toByte()
            header[1] = 'D'.code.toByte()
            header[2] = '3'.code.toByte()
            header[3] = 3  // 版本2.3
            header[4] = 0  // 修订版本
            header[5] = 0  // 标志

            // 标签大小 (不包括头10字节) - 设置为0，简单起见
            header[6] = 0
            header[7] = 0
            header[8] = 0
            header[9] = 0

            outputStream.write(header)
        } catch (e: IOException) {
            e.printStackTrace()
        }
    }

    /**
     * 写入ID3v1标签到MP3文件
     */
    private fun writeID3v1Tag(outputStream: OutputStream) {
        try {
            val tag = ByteArray(128)
            tag[0] = 'T'.code.toByte()
            tag[1] = 'A'.code.toByte()
            tag[2] = 'G'.code.toByte()

            // 标题 (30字节)
            val title = "Recorded Audio"
            System.arraycopy(title.toByteArray(), 0, tag, 3, Math.min(title.length, 30))

            // 艺术家 (30字节)
            val artist = "App Recording"
            System.arraycopy(artist.toByteArray(), 0, tag, 33, Math.min(artist.length, 30))

            // 专辑 (30字节)
            val album = "App Recordings"
            System.arraycopy(album.toByteArray(), 0, tag, 63, Math.min(album.length, 30))

            // 年份 (4字节)
            val year = java.util.Calendar.getInstance().get(java.util.Calendar.YEAR).toString()
            System.arraycopy(year.toByteArray(), 0, tag, 93, Math.min(year.length, 4))

            // 注释 (28字节) + 0 + 曲目号
            val comment = "Recorded with AndroidLame"
            System.arraycopy(comment.toByteArray(), 0, tag, 97, Math.min(comment.length, 28))

            // 流派 (1字节)
            tag[127] = 0  // 未知流派

            outputStream.write(tag)
        } catch (e: IOException) {
            e.printStackTrace()
        }
    }


    private fun addAdtsHeader(packet: ByteArray, packetLen: Int, sampleRate: Int): ByteArray {
        val adtsHeader = ByteArray(7)
        val profile = 2
        val freqIdx = getAacSampleRateIndex(sampleRate) // 采样率索引
        val chanCfg = 2

        adtsHeader[0] = 0xFF.toByte()
        adtsHeader[1] = 0xF1.toByte()
        adtsHeader[2] = ((profile - 1) shl 6 or (freqIdx shl 2) or (chanCfg shr 2)).toByte()
        adtsHeader[3] = ((chanCfg and 3) shl 6 or (packetLen shr 11)).toByte()
        adtsHeader[4] = ((packetLen and 0x7FF) shr 3).toByte()
        adtsHeader[5] = ((packetLen and 7) shl 5 or 0x1F).toByte()
        adtsHeader[6] = 0xFC.toByte()

        return adtsHeader + packet
    }

    private fun getAacSampleRateIndex(sampleRate: Int): Int {
        return when (sampleRate) {
            96000 -> 0
            88200 -> 1
            64000 -> 2
            48000 -> 3
            44100 -> 4
            32000 -> 5
            24000 -> 6
            22050 -> 7
            16000 -> 8
            12000 -> 9
            11025 -> 10
            8000 -> 11
            else -> 4 // 默认44100Hz
        }
    }

    private fun initMediacodec(): MediaCodec {
        val mediaFormat = MediaFormat.createAudioFormat(MediaFormat.MIMETYPE_AUDIO_AAC, 44100, 2)
        mediaFormat.setInteger(MediaFormat.KEY_BIT_RATE, 192000)
        mediaFormat.setInteger(
            MediaFormat.KEY_AAC_PROFILE,
            MediaCodecInfo.CodecProfileLevel.AACObjectLC
        )
        mediaFormat.setInteger(MediaFormat.KEY_MAX_INPUT_SIZE, 16384)

        val mediaCodec = MediaCodec.createEncoderByType(MediaFormat.MIMETYPE_AUDIO_AAC)
        mediaCodec.configure(mediaFormat, null, null, MediaCodec.CONFIGURE_FLAG_ENCODE)
        return mediaCodec
    }

    private fun MediaCodec.putPcmData(pts: Long, buffer: ByteArray, size: Int) {
        val inputBufferIndex: Int = dequeueInputBuffer(0)
        if (inputBufferIndex >= 0) {
            val byteBuffer = getInputBuffer(inputBufferIndex) ?: return
            //                Log.e("TAG", "putPcmData: byteBuffer:"+byteBuffer.remaining() +"   size:"+size);
            byteBuffer.clear()
            normalizePcm(buffer, 0.8f)
            byteBuffer.put(buffer)
            queueInputBuffer(inputBufferIndex, 0, size, pts, 0)
        } else {
//            queueInputBuffer(inputBufferIndex, 0, 0, pts, MediaCodec.BUFFER_FLAG_END_OF_STREAM)
        }
    }
    private fun normalizePcm(data: ByteArray, factor: Float = 1.2f) {
        for (i in data.indices step 2) {
            val sample = ((data[i + 1].toInt() shl 8) or (data[i].toInt() and 0xFF)).toShort()
            val normalizedSample = (sample * factor).toInt().toShort()
            data[i] = (normalizedSample and 0xFF).toByte()
            data[i + 1] = ((normalizedSample.toInt() shr 8) and 0xFF).toByte()
        }
    }

    private fun calculateDb(buffer: ByteArray): Double {
        // 对于 16 位音频数据
        val shortArray = ShortArray(buffer.size / 2)

        // 将字节数组转换为短整型数组
        for (i in shortArray.indices) {
            shortArray[i] = (buffer[i * 2].toInt() and 0xFF or
                    (buffer[i * 2 + 1].toInt() shl 8)).toShort()
        }

        // 计算均方根值 (RMS)
        var sum = 0.0
        for (sample in shortArray) {
            sum += sample * sample
        }

        // 避免除以零或空数组
        if (shortArray.isEmpty()) return 0.0

        val rms = sqrt(sum / shortArray.size)

        // 计算分贝值，参考值为 0dB = 32767 (16位音频的最大值)
        // 避免对0取对数
        if (rms < 1) return 0.0

        // 计算分贝值
        val db = 20 * log10(rms / 32767)

        // 将负分贝值映射到0-90范围
        return if (db < 0) {
            // 通常环境噪音在 -60dB 到 0dB 之间
            // 将 -60dB 映射到 0，0dB 映射到 90
            90 + db * 1.5
        } else {
            // 正分贝值（罕见，通常表示过载）
            90.0
        }
    }

    private fun writeEmptyWaveFileHeader(fos: FileOutputStream) {
        val emptyHeader = ByteArray(44)
        try {
            fos.write(emptyHeader)
        } catch (e: IOException) {
            e.printStackTrace()
        }
    }


    private fun writeWaveFileHeader(
        fileOutputStream: FileOutputStream,
        totalAudioLen: Long,
        totalDataLen: Long,
        sampleRate: Int,
        bitsPerSample: Int,
        channels: Int,
        byteRate: Long
    ) {
        runCatching {
            fileOutputStream.channel.use { channel ->
                val header = ByteArray(44)

                header[0] = 'R'.code.toByte() // RIFF/WAVE header
                header[1] = 'I'.code.toByte()
                header[2] = 'F'.code.toByte()
                header[3] = 'F'.code.toByte()
                header[4] = (totalDataLen and 0xff).toByte()
                header[5] = ((totalDataLen shr 8) and 0xff).toByte()
                header[6] = ((totalDataLen shr 16) and 0xff).toByte()
                header[7] = ((totalDataLen shr 24) and 0xff).toByte()
                header[8] = 'W'.code.toByte() // WAVE header
                header[9] = 'A'.code.toByte()
                header[10] = 'V'.code.toByte()
                header[11] = 'E'.code.toByte()
                header[12] = 'f'.code.toByte()
                header[13] = 'm'.code.toByte()
                header[14] = 't'.code.toByte()
                header[15] = ' '.code.toByte()
                header[16] = 16
                header[17] = 0
                header[18] = 0
                header[19] = 0
                header[20] = 1
                header[21] = 0
                header[22] = channels.toByte()
                header[23] = 0
                header[24] = (sampleRate and 0xff).toByte()
                header[25] = ((sampleRate shr 8) and 0xff).toByte()
                header[26] = ((sampleRate shr 16) and 0xff).toByte()
                header[27] = ((sampleRate shr 24) and 0xff).toByte()
                header[28] = (byteRate and 0xff).toByte()
                header[29] = ((byteRate shr 8) and 0xff).toByte()
                header[30] = ((byteRate shr 16) and 0xff).toByte()
                header[31] = ((byteRate shr 24) and 0xff).toByte()
                header[32] = (channels * 16 / 8).toByte() // block align
                header[33] = 0
                header[34] = bitsPerSample.toByte()
                header[35] = 0
                header[36] = 'd'.code.toByte() // data chunk
                header[37] = 'a'.code.toByte()
                header[38] = 't'.code.toByte()
                header[39] = 'a'.code.toByte()
                header[40] = (totalAudioLen and 0xff).toByte()
                header[41] = ((totalAudioLen shr 8) and 0xff).toByte()
                header[42] = ((totalAudioLen shr 16) and 0xff).toByte()
                header[43] = ((totalAudioLen shr 24) and 0xff).toByte()

                channel.position(0)
                channel.write(java.nio.ByteBuffer.wrap(header))
            }
        }

    }


    fun release(){
        mainScope.cancel()
    }
    interface OnRecordListener {
        fun recordByte(audioData: ByteArray, readSize: Int)
    }


}