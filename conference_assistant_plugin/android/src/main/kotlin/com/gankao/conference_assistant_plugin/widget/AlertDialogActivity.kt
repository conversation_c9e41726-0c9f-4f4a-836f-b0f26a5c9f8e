package com.gankao.conference_assistant_plugin.widget

import android.content.Intent
import android.icu.lang.UCharacter.GraphemeClusterBreak.L
import android.os.Bundle
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import com.gankao.conference_assistant_plugin.speech.AliSpeechHelper
import com.gankao.conference_assistant_plugin.utils.Constant
import com.gk.logcat.LogUtil

// AlertDialogActivity.kt
class AlertDialogActivity : AppCompatActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        LogUtil.e("AlertDialogActivity onCreate")
//        是否要停止录音，取消录音将继续，可在通知栏里返回剪流查看
        AlertDialog.Builder(this)
            .setTitle("选择操作")
//            .setMessage("")
            .setPositiveButton("结束录音") { _, _ ->
                // 通知 Service：用户确认关闭
//                sendBroadcast(Intent("ACTION_CLOSE_FLOATING_WINDOW"))
                AliSpeechHelper.stopDialog(isUser = true)
                finish()
            }
            .setNegativeButton("隐藏小窗") { _, _ ->
                Toast.makeText(this, "会议助理将在后台继续录音，可在通知栏里返回剪流查看", Toast.LENGTH_SHORT).show()
                Constant.isShowAlertDialogActivity = true
                finish()
            }
            .setOnCancelListener {
                finish()
            }
            .show()
    }
}