package com.gankao.conference_assistant_plugin.widget

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import android.widget.ScrollView

class NonScrollableScrollView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null
) : ScrollView(context, attrs) {

    override fun onInterceptTouchEvent(ev: MotionEvent?): <PERSON><PERSON><PERSON> {
        // 不拦截触摸事件，禁止手动滚动
        return false
    }

    override fun onTouchEvent(ev: MotionEvent?): <PERSON><PERSON><PERSON> {
        // 不消费触摸事件，彻底禁用滚动
        return false
    }
}