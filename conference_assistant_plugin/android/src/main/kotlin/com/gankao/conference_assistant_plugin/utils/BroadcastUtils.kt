package com.gankao.conference_assistant_plugin.utils

import android.content.Context
import android.content.Intent
import com.gk.logcat.LogUtil


// 1 录音中
// 2 暂停
// 3 结束
fun Context.sendRecording(state: Int){
    LogUtil.e("发送广播 sendRecording: $state")
    val intent = Intent("com.cutflow.recording");
//    intent.setPackage("com.gankao.junior.cutflow")
    intent.putExtra("state", state);
    this.sendBroadcast(intent)
}

fun Context.sendNativeParams(params: String){
    LogUtil.e("发送广播 sendNativeParams: $params")
    val intent = Intent("com.cutflow.flutter_params");
//    intent.setPackage("com.gankao.junior.cutflow")
    intent.putExtra("params", params);
    this.sendBroadcast(intent)
}