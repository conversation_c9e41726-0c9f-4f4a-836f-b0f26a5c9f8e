package com.gankao.conference_assistant_plugin.utils

import android.content.ContentResolver
import android.content.Context
import android.net.Uri
import android.util.Log
import com.qiniu.android.http.ResponseInfo
import com.qiniu.android.storage.Configuration
import com.qiniu.android.storage.UploadManager
import com.qiniu.android.storage.UploadOptions
import kotlinx.coroutines.suspendCancellableCoroutine
import java.io.File
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException
import kotlin.coroutines.suspendCoroutine
import com.gk.logcat.LogUtil
import com.gk.logcat.LogUtil.context
import com.qiniu.android.storage.UpProgressHandler

object QiNiuHelper {

    suspend fun uploadFileToQiniuSuspend(token: String, file: File, path: String, onProgress:((Double)->Unit)? = null): ResponseInfo? {
        if (file.length() == 0L) {
//            runCatching { file.delete() }
            return null
        }
        return suspendCancellableCoroutine { continuation ->
            uploadFileToQiniu(token, file.absolutePath, "gk_assistant/$path", onResult = {
                if (it?.isOK == true) {
//                    runCatching { file.delete() }
                    continuation.resume(it)
                } else {
                    continuation.resumeWithException(Exception(it?.error ?: "Upload failed"))
                }
            },onProgress = {
                onProgress?.invoke(it)
            })
        }
    }

    suspend fun uploadUriToQiniuSuspend(context: Context, token: String, uri: Uri, path: String, onProgress:((Double)->Unit)? = null): ResponseInfo? {
        return suspendCancellableCoroutine { continuation ->
            uploadUriToQiniu(context, token, uri, "gk_assistant/$path", onResult = {
                if (it?.isOK == true) {
//                    runCatching { file.delete() }
                    continuation.resume(it)
                } else {
                    continuation.resumeWithException(Exception(it?.error ?: "Upload failed"))
                }
            }, onProgress = {
                onProgress?.invoke(it)
            })
        }
    }

    /**
     *
     * @param token String
     * @param file File
     * @param path String 七牛云存储路径 /packageName/uid/fileName
     * @param onSuccess Function1<ResponseInfo?, Unit>?
     * @param onFailure Function1<String, Unit>?
     */
    fun uploadFileToQiniu(token: String, file: File, path: String, onSuccess: ((ResponseInfo?) -> Unit)? = null, onFailure: ((String) -> Unit)? = null, onProgress:((Double)->Unit)? = null) {
        if (file.length() == 0L){
            runCatching {
                file.delete()
            }
            return
        }
        uploadFileToQiniu(token, file.absolutePath, "gk_assistant/$path", onResult = {
            if (it?.isOK == true) {
                onSuccess?.invoke(it)
                runCatching { file.delete() }
            } else {
                onFailure?.invoke(it?.error ?: "error")
            }
        }, onProgress = {
            onProgress?.invoke(it)
        })
    }


    private fun uploadFileToQiniu(
        qiuNiuToken: String,
        filePath: String,
        fileName: String,
        onResult: (ResponseInfo?) -> Unit,
        onProgress: (Double)->Unit
    ){
        val progressHandler = UpProgressHandler { key, percent ->
            LogUtil.i("QiNiuHelper", "Upload filePath:$filePath fileName:$fileName progress: $key - ${percent * 100}%")
            onProgress(percent)
        }

        val options = UploadOptions(hashMapOf(), null, false, progressHandler, null)

        configUploadManager().put(
            filePath,
            fileName,
            qiuNiuToken,
            { key, info, response ->
                Log.i("TAG", "onResponse: response result = ${info}")
                onResult(info)
            },
            options
        )
    }

    private fun uploadUriToQiniu(
        context: Context,
        qiuNiuToken: String,
        uri: Uri,
        fileName: String,
        onResult: (ResponseInfo?) -> Unit,
        onProgress: (Double)->Unit
    ){
        val progressHandler = UpProgressHandler { key, percent ->
            LogUtil.i("QiNiuHelper", "Upload uri:$uri fileName:$fileName progress: $key - ${percent * 100}%")
            onProgress(percent)
        }
        val options = UploadOptions(hashMapOf(), null, false, progressHandler, null)
        configUploadManager()
            .put(uri, context.contentResolver, fileName, qiuNiuToken,{ key, info, response ->
            LogUtil.i("QiNiuHelper", "onResponse: response result = ${info}")
            onResult(info)
        }, options)
    }

    private fun configUploadManager(): UploadManager {
        val config = Configuration.Builder()
            .chunkSize(512 * 512) //分片上传时，每片的大小。 默认256K
            .putThreshold(512)// 启用分片上传阀值。默认512K
            .connectTimeout(10) // 链接超时。默认10秒
            .responseTimeout(60) // 服务器响应超时。默认60秒
//                .zone(FixedZone.zone0) // 设置区域，指定不同区域的上传域名、备用域名、备用IP。
            .build()
        return UploadManager(config)
    }
}