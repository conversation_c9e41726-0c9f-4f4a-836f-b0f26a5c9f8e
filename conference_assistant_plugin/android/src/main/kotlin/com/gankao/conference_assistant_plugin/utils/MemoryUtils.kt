package com.gankao.conference_assistant_plugin.utils

import android.app.ActivityManager
import android.content.Context
import android.os.Debug
import com.gk.logcat.LogUtil

object MemoryUtils {

    fun getAppMemoryUsageRatio(): Float {
        val runtime = Runtime.getRuntime()
        val usedMem = runtime.totalMemory() - runtime.freeMemory()
        val maxMem = runtime.maxMemory()
        return usedMem.toFloat() / maxMem // 返回 0~1 的占用比
    }


    fun getAppActualMemoryMB(): Int {
        val memoryInfo = Debug.MemoryInfo()
        Debug.getMemoryInfo(memoryInfo)
        return memoryInfo.totalPss / 1024 // 单位 MB，准确
    }

    fun isAppMemoryTooHigh(context: Context): Boolean {
        val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        val memInfo = ActivityManager.MemoryInfo()
        activityManager.getMemoryInfo(memInfo)

        val totalSysMem = memInfo.totalMem
        val availSysMem = memInfo.availMem
        val usedSysMem = totalSysMem - availSysMem

        val memoryInfo = Debug.MemoryInfo()
        Debug.getMemoryInfo(memoryInfo)
        val appPssMB = memoryInfo.totalPss / 1024
        val appMemRatio = appPssMB * 1024 * 1024f / totalSysMem
        LogUtil.e("isAppMemoryTooHigh: totalSysMem:$totalSysMem availSysMem:$availSysMem appPssMB:$appPssMB  appMemRatio:$appMemRatio")
        return appMemRatio > 0.25f // 占系统内存 > 25%，可能过高
    }
}