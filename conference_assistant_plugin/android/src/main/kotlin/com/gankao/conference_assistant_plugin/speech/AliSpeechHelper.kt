package com.gankao.conference_assistant_plugin.speech

import android.Manifest
import android.R.attr.path
import android.R.attr.text
import android.R.attr.type
import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.media.AudioAttributes
import android.media.AudioFormat
import android.media.AudioManager
import android.media.AudioPlaybackCaptureConfiguration
import android.media.AudioRecord
import android.media.MediaRecorder
import android.media.audiofx.NoiseSuppressor
import android.media.projection.MediaProjection
import android.media.projection.MediaProjectionManager
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.util.Log
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.annotation.RequiresPermission
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat.getSystemService
import androidx.fragment.app.FragmentActivity
import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONException
import com.alibaba.fastjson.JSONObject
import com.alibaba.idst.nui.AsrResult
import com.alibaba.idst.nui.Constants
import com.alibaba.idst.nui.INativeNuiCallback
import com.alibaba.idst.nui.KwsResult
import com.alibaba.idst.nui.NativeNui
import com.gankao.conference_assistant_plugin.FloatingWindowService
import com.gankao.conference_assistant_plugin.speech.AliSpeechHelper
import com.gankao.conference_assistant_plugin.speech.AliSpeechHelper.PAUSED
import com.gankao.conference_assistant_plugin.speech.AliSpeechHelper.PREPARE
import com.gankao.conference_assistant_plugin.speech.AliSpeechHelper.RECORDING
import com.gankao.conference_assistant_plugin.speech.AliSpeechHelper.isInitSpeex
import com.gankao.conference_assistant_plugin.speech.AliSpeechHelper.isSaveAudio
import com.gankao.conference_assistant_plugin.speech.AliSpeechHelper.recordBufferSize
import com.gankao.conference_assistant_plugin.speech.ContentModel
import com.gankao.conference_assistant_plugin.speech.RecordInfoModel
import com.gankao.conference_assistant_plugin.utils.AudioProcessor
import com.gankao.conference_assistant_plugin.utils.AudioRecordUtil
import com.gankao.conference_assistant_plugin.utils.AudioUtils
import com.gankao.conference_assistant_plugin.utils.Constant
import com.gankao.conference_assistant_plugin.utils.MMKVHelper
import com.gk.logcat.LogUtil
import com.gk.logcat.LogUtil.context
import com.gk.speex.jnibridge.SpeexJNIBridge
import com.naman14.androidlame.AndroidLame
import com.naman14.androidlame.LameBuilder
import kotlinx.coroutines.CompletableDeferred
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.io.OutputStream
import java.nio.ByteBuffer
import java.nio.ByteOrder
import kotlin.experimental.and
import kotlin.time.Duration
import io.flutter.plugin.common.PluginRegistry
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.newSingleThreadContext
import java.util.concurrent.atomic.AtomicBoolean
import kotlin.jvm.java

private const val TAG = "AliSpeechHelper"

data class ContentModel(
    val type: String,
    val curTaskId: String = "",
    val result: String = "",
    val resultCode: Int = -1,
    val allResponse: String = ""
)
data class RecordInfoModel(
    val path: String = "",
    val duration: Int = 0
)
@SuppressLint("MissingPermission")
object AliSpeechHelper {
    private var nui_instance : NativeNui? = null
    private val mainScope = MainScope()
    private var g_url: String = ""
    private var mDebugPath = ""
    private var mStopping = false;

    private var curTaskId = ""

    private var mAudioRecorder:AudioRecord? = null
    private val SAMPLE_RATE = 16000;
    private val WAVE_FRAM_SIZE = 20 * 2 * 1 * SAMPLE_RATE / 1000; //20ms audio for 16k/16bit/mono

    private val AUDIO_FORMAT = "pcm"
    private val g_appkey = "6Y9XRSymf0RhXAKw";
    private var g_token = "caff4df9cddb4e9d82777c07946bca46";
    private val g_ak = "";
    private val g_sts_token = "";
    private val g_sk = "";
//    private val g_sk = "8038cf644bae44d59d4460e3980dec5a";
//    private val g_sk = "8038cf644bae44d59d4460e3980dec5a";

    // task_id to content 全部录音
    private val _dialogContentStateFlow = MutableStateFlow("" to "")
    val dialogContentStateFlow = _dialogContentStateFlow.asStateFlow()

    private val _contentSharedFlow = MutableSharedFlow<ContentModel>()
    val contentSharedFlow = _contentSharedFlow.asSharedFlow()

    private val _audioRMSVolumeSharedFlow = MutableSharedFlow<Float>()
    val audioRMSVolumeSharedFlow = _audioRMSVolumeSharedFlow.asSharedFlow()


    private val _audioRecordStopStateFlow = MutableStateFlow<RecordInfoModel>(RecordInfoModel())
    val audioRecordStopStateFlow = _audioRecordStopStateFlow.asStateFlow()


    private val _audioDurationStateFlow = MutableStateFlow<Int>(0)
    val audioDurationStateFlow = _audioDurationStateFlow.asStateFlow()
    private var audioDurationJob: Job? = null

    // 初始化：PREPARE
    // 开始: STARTED
    // 暂停: PAUSED
    // 停止: STOPPED
    const val PREPARE = "PREPARE"
    const val RECORDING = "RECORDING"
    const val PAUSED = "PAUSED"
    const val STOPPED = "STOPPED"
    const val ERROR = "ERROR"
    private val _recordStateFlow = MutableStateFlow<String>(PREPARE)
    val recordStateFlow = _recordStateFlow.asStateFlow()

    private var isPause = false
    var audioSource = 1
        private set

    val isInvokeFocusBehavior get() = audioSource == 1
    // 一些录制参数
    val recordBufferSize by lazy {
        val minBufferSize = AudioRecord.getMinBufferSize(
            SAMPLE_RATE,
            AudioFormat.CHANNEL_IN_MONO,
            AudioFormat.ENCODING_PCM_16BIT
        )
        minBufferSize * 2
    }
    private var androidLame: AndroidLame? = null
    private var outputFile: File? = null
    private var channelCount = 1
    private var outputStream: FileOutputStream? = null

    // MP3缓冲区 (1.25倍PCM大小应该足够)
    private var mp3Buffer: ByteArray? = null
    var isSaveAudio = true
    var isInitSpeex = false
    private val _isCancelStateFlow = MutableStateFlow<Boolean>(false)

    // 添加后台模式标志
    private var isBackgroundModeEnabled = false



    // 是否是stop，为了兼容 sdk error 情况
    private val _isUserStopStateFlow = MutableStateFlow<Boolean>(false)
    private val _retryRecordSharedFlow = MutableSharedFlow<String>()
    private val retry_count = 3
    private var recordType = 1
    
    // 当前是否在后台
    var isBackground = false


    fun setToken(token: String){
        g_token = token
    }

    init {
        mainScope.launch {
            _retryRecordSharedFlow.collectLatest{
                LogUtil.e(TAG, "retryRecordSharedFlow: _recordStateFlow:${_recordStateFlow.value}  $it")
                if (_recordStateFlow.value == STOPPED){
                    return@collectLatest
                }
                onlyStopDialogSuspend(true, false)
                delay(100)
                val ret = startDialog(
                    audioSource = recordType,
                    isRetry = true
                )
                LogUtil.e(TAG, "retryRecordSharedFlow: startDialog $ret $recordType")
            }
        }
    }
    
    
    @Deprecated("测试用的")
    fun testRetry(){
        mainScope.launch {
            _retryRecordSharedFlow.emit("test")
        }
    }
    
    fun init(context: Context){

        _audioDurationStateFlow.value = 0
        _dialogContentStateFlow.value = "" to ""
        processor = null
        _recordStateFlow.value = PREPARE


        curTaskId = ""
        mStopping = false;
        mDebugPath = context.cacheDir.absolutePath + "/debug_" + System.currentTimeMillis();
        Utils.createDir(mDebugPath);

        //初始化SDK，注意用户需要在Auth.getTicket中填入相关ID信息才可以使用。
        nui_instance = NativeNui()
        val callback = getINativeNuiCallback()
        val ret = nui_instance?.initialize(callback, genInitParams("", mDebugPath),
            Constants.LogLevel.LOG_LEVEL_VERBOSE, true)?:-1

        if (ret == Constants.NuiResultCode.SUCCESS){
            LogUtil.i(TAG, "Initialize success")
        }else{
            val msg = Utils.getMsgWithErrorCode(ret, "init")
            LogUtil.e(TAG, "Initialize failed: $msg")
        }
    }

    private fun getINativeNuiCallback(): INativeNuiCallback{
        return object: INativeNuiCallback{
            override fun onNuiEventCallback(
                event: Constants.NuiEvent?,
                resultCode: Int,
                arg2: Int,
                kwsResult: KwsResult?,
                asrResult: AsrResult?
            ) {
                LogUtil.i(TAG, "isPause: "+isPause+"  event=" + event + " resultCode=" + resultCode +" allResponse: "+asrResult?.allResponse);
                // asrResult包含task_id，task_id有助于排查问题，请用户进行记录保存。
                if (isPause){
                    return
                }
                if (event == Constants.NuiEvent.EVENT_TRANSCRIBER_STARTED) {
                    // EVENT_TRANSCRIBER_STARTED 为V2.6.3版本新增
//                    showText(asrView, "EVENT_TRANSCRIBER_STARTED");
                    val jsonObject = JSON.parseObject(asrResult?.allResponse?:"");
                    val header = jsonObject.getJSONObject("header");
//                    curTaskId = header.getString("task_id");
//                    mainScope.launch {
//                        _contentSharedFlow.emit(ContentModel("EVENT_TRANSCRIBER_STARTED", curTaskId, result = "", resultCode, asrResult?.allResponse?:""))
//                        _dialogContentStateFlow.value = curTaskId to ""
//                    }
                } else if (event == Constants.NuiEvent.EVENT_TRANSCRIBER_COMPLETE) {
//                    setButtonState(startButton, true);
//                    setButtonState(cancelButton, false);
                    // todo 结束了
                    val text = asrResult?.allResponse
//                    appendText(asrView, asrResult.allResponse);
                    mainScope.launch {
                        _contentSharedFlow.emit(ContentModel("EVENT_TRANSCRIBER_COMPLETE", curTaskId, result = "", resultCode, asrResult?.allResponse?:""))
                    }
                    mStopping = false;
                } else if (event == Constants.NuiEvent.EVENT_ASR_PARTIAL_RESULT || event == Constants.NuiEvent.EVENT_SENTENCE_END) {
//                    if (mStopping) {
//                         asrResult?.asrResult
////                        appendText(asrView, asrResult.asrResult);
//                    } else {
//                         asrResult?.asrResult
////                        showText(asrView, asrResult.asrResult);
//                    }
                    val text = asrResult?.asrResult
                    val jsonObject = JSON.parseObject(asrResult?.allResponse?:"");
                    val payload = jsonObject.getJSONObject("payload");
                    val result = payload.getString("result");
                    val header = jsonObject.getJSONObject("header");
                    val taskId = header.getString("task_id");
                    LogUtil.e(TAG, "$event: $result")
                    mainScope.launch {
                        _contentSharedFlow.emit(ContentModel(event.toString(), taskId, result = result, resultCode, asrResult?.allResponse?:""))
                        if (event == Constants.NuiEvent.EVENT_SENTENCE_END){
                            _dialogContentStateFlow.update { it.first to it.second+result }
                        }
                    }
//                    showText(resultView, result);
                } else if (event == Constants.NuiEvent.EVENT_VAD_START) {
//                    showText(asrView, "EVENT_VAD_START");
                    LogUtil.e(TAG, "EVENT_VAD_START")
                    mainScope.launch {
                        _contentSharedFlow.emit(ContentModel("EVENT_VAD_START"))
                    }
                } else if (event == Constants.NuiEvent.EVENT_VAD_END) {
//                    appendText(asrView, "EVENT_VAD_END");
                    LogUtil.e(TAG, "EVENT_VAD_END")
                    mainScope.launch {
                        _contentSharedFlow.emit(ContentModel("EVENT_VAD_END"))
                    }
                } else if (event == Constants.NuiEvent.EVENT_ASR_ERROR) {
                    // asrResult在EVENT_ASR_ERROR中为错误信息，搭配错误码resultCode和其中的task_id更易排查问题，请用户进行记录保存。
//                    appendText(asrView, asrResult.asrResult);
                    val text = asrResult?.asrResult
                    val msg_text = Utils.getMsgWithErrorCode(resultCode, "start");
                    LogUtil.e(TAG, "EVENT_ASR_ERROR: $text")
//                    setButtonState(startButton, true);
//                    setButtonState(cancelButton, false);
                    mainScope.launch {
                        _contentSharedFlow.emit(ContentModel("EVENT_ASR_ERROR", resultCode = resultCode, allResponse = text?:""))
                        _retryRecordSharedFlow.emit("EVENT_ASR_ERROR: resultCode: $resultCode: msg_text: $msg_text")
                    }
                    mStopping = false;
                } else if (event == Constants.NuiEvent.EVENT_MIC_ERROR) {
                    // EVENT_MIC_ERROR表示2s未传入音频数据，请检查录音相关代码、权限或录音模块是否被其他应用占用。
                    val msg_text = Utils.getMsgWithErrorCode(resultCode, "start");
                    LogUtil.e(TAG, "EVENT_MIC_ERROR: $msg_text")
                    mainScope.launch {
                        _contentSharedFlow.emit(ContentModel("EVENT_MIC_ERROR", resultCode = resultCode))
                        _retryRecordSharedFlow.emit("EVENT_MIC_ERROR: resultCode: $resultCode: msg_text: $msg_text")
                    }
                    mStopping = false;
                    // 此处也可重新启动录音模块
                } else if (event == Constants.NuiEvent.EVENT_DIALOG_EX) { /* unused */
                    LogUtil.i(TAG, "dialog extra message = " + asrResult?.asrResult);
                    mainScope.launch {
                        _contentSharedFlow.emit(ContentModel("EVENT_DIALOG_EX", resultCode = resultCode, allResponse = asrResult?.asrResult?:""))
                    }
                }
            }

            override fun onNuiNeedAudioData(buffer: ByteArray?, len: Int): Int {
                if (mAudioRecorder == null || buffer == null) {
                    return -1
                }
                if (mAudioRecorder?.state != AudioRecord.STATE_INITIALIZED) {
                    LogUtil.e(TAG, "audio recorder not init")
                    return -1
                }
                if (isPause){
                    return len
                }
// 送入SDK
                val audio_size = mAudioRecorder?.read(buffer, 0, len)?:-1
                if (isSaveAudio && !isPause){
                        if (!isInitSpeex){
                            LogUtil.e(TAG, "初始化SpeexJNIBridge init  $isInitSpeex $recordBufferSize")
                            SpeexJNIBridge.init(recordBufferSize, SAMPLE_RATE)
                            processor = AudioProcessor(recordBufferSize)
                            isInitSpeex = true
                        }
                        processor?.let {
                            it.processAudio(buffer){ frame ->
                                LogUtil.e("jni speex SpeexJNIBridge_denoise 调用jni denoise  ${it.frameSize} ${frame.size}  ${buffer.size} $audio_size $len");
                                SpeexJNIBridge.denoise(frame)
                                writeRecordToMP3(frame.size, frame)
                            }
                        } ?: run {
                            writeRecordToMP3(audio_size, buffer)
                        }
                }
                return audio_size;
            }

            override fun onNuiAudioStateChanged(p0: Constants.AudioState?) {
                LogUtil.i(TAG, "onNuiAudioStateChanged")
                when (p0) {
                    Constants.AudioState.STATE_OPEN -> {
                        LogUtil.i(TAG, "audio recorder start")
                        mAudioRecorder?.startRecording()
                        audioDurationJob?.cancel()
                        audioDurationJob = null
                        audioDurationJob = mainScope.launch(Dispatchers.IO) {
                            while (true){
                                delay(1000)
                                if (!isPause){
                                    val audioDuration = _audioDurationStateFlow.value + 1
                                    _audioDurationStateFlow.update { audioDuration }
                                    LogUtil.i(TAG, "audio _audioDurationStateFlow: ${audioDuration}")
                                    outputFile?.name?.substringBefore("_")?.let {
                                        MMKVHelper.saveRecordingDuration(it, audioDuration)
                                    }
                                }
                            }
                        }
                        LogUtil.i(TAG, "audio recorder start done")
                    }
                    Constants.AudioState.STATE_CLOSE -> {
                        LogUtil.i(TAG, "audio recorder close")
                        mAudioRecorder?.release()
                        mAudioRecorder = null

                    }
                    Constants.AudioState.STATE_PAUSE -> {
                        LogUtil.i(TAG, "audio recorder pause")
                        mAudioRecorder?.stop()
                        writeOutputFinish()
                    }
                    else -> {
                        LogUtil.i(TAG, "audio recorder unknown state")
                    }
                }
            }

            override fun onNuiAudioRMSChanged(p0: Float) {
                mainScope.launch(Dispatchers.Main) {
                    if (!isPause){
                        _audioRMSVolumeSharedFlow.emit(p0)
                    }
                }
            }

            override fun onNuiVprEventCallback(p0: Constants.NuiVprEvent?) {
                LogUtil.i(TAG, "onNuiVprEventCallback event $p0");
            }
        }
    }



    var mediaProjection: MediaProjection? = null
    var mediaProjectionCallback: MediaProjection.Callback? = null
    fun startScreenCapture(context: Context, resultCode: Int, data: Intent){
        val mediaProjectionManager = context.getSystemService(Context.MEDIA_PROJECTION_SERVICE) as MediaProjectionManager
        mediaProjection = mediaProjectionManager.getMediaProjection(resultCode, data)
        mediaProjectionCallback = object : MediaProjection.Callback() {
            override fun onStop() {
                super.onStop()
                Log.d("ScreenCapture", "MediaProjection stopped")
                stopScreenCapture()
            }
        }
        mediaProjection?.registerCallback(mediaProjectionCallback!!, Handler(Looper.getMainLooper()))
    }

    fun stopScreenCapture() {
        mediaProjectionCallback?.let {
            mediaProjection?.unregisterCallback(it)
        }
        mediaProjection?.stop()
        mediaProjection = null
        mediaProjectionCallback = null
    }


    // registerForActivityResult 方式获取
    var screenCaptureLauncher: ActivityResultLauncher<Intent>? = null
    fun initScreenCaptureLauncher(activity: FragmentActivity){
        screenCaptureLauncher = activity.registerForActivityResult(ActivityResultContracts.StartActivityForResult()){ result->
            if (result.resultCode == Activity.RESULT_OK && result.data != null) {
                startScreenCapture(activity, result.resultCode, result.data!!)
                // 录屏准备完成
                if (mediaProjection == null){
                    Log.e("ScreenCapture", "录屏权限请求失败")
                    completableDeferredEnable?.complete(false)
                }else{
                    Log.e("ScreenCapture", "录屏权限请求成功")
                    completableDeferredEnable?.complete(true)
                }
            } else {
                Log.e("ScreenCapture", "用户拒绝录屏")
                completableDeferredEnable?.complete(false)
            }
            completableDeferredEnable = null
        }
    }

    var activityResultListener : PluginRegistry.ActivityResultListener? = null

    fun getResult(activity: Activity): PluginRegistry.ActivityResultListener{
        activityResultListener = object : PluginRegistry.ActivityResultListener{
            override fun onActivityResult(
                requestCode: Int,
                resultCode: Int,
                data: Intent?
            ): Boolean {
                if (requestCode != 100001){
                    completableDeferredEnable?.complete(false)
                    completableDeferredEnable = null
                    return false
                }
                mainScope.launch {
                    if (resultCode == Activity.RESULT_OK && data != null) {
                        LogUtil.e("getResult: resultCode: $resultCode mediaProjection: $mediaProjection  $data")
                        notifyCompletableDeferredEnable = CompletableDeferred()

                        val intent = Intent(activity, FloatingWindowService::class.java)
                        intent.putExtra("audioSourceType",2)
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                            activity.startForegroundService(intent)
                        } else {
                            activity.startService(intent)
                        }
                        LogUtil.e("ScreenCapture", "创建前台服务 createService ==== ")
                        val createService = notifyCompletableDeferredEnable?.await()
                        LogUtil.e("ScreenCapture", "创建前台服务 createService ==== 2222")
                        notifyCompletableDeferredEnable = null
                        LogUtil.e("ScreenCapture", "创建前台服务 createService $createService")
                        startScreenCapture(
                            activity,
                            resultCode,
                            data
                        )
                        // 录屏准备完成
                        if (mediaProjection == null){
                            Log.e("ScreenCapture", "录屏权限请求失败")
                            completableDeferredEnable?.complete(false)
                        }else{
                            Log.e("ScreenCapture", "录屏权限请求成功")
                            completableDeferredEnable?.complete(true)
                        }
                    } else {
                        Log.e("ScreenCapture", "用户拒绝录屏")
                        completableDeferredEnable?.complete(false)
                    }
                    completableDeferredEnable = null
                }
                return true
            }
        }
        return activityResultListener!!
    }


    private var completableDeferredEnable: CompletableDeferred<Boolean>? = null
    var notifyCompletableDeferredEnable: CompletableDeferred<Boolean>? = null
    suspend fun requestScreenCapture(activity: Activity): Boolean{
        if (screenCaptureLauncher == null && activityResultListener == null){
            LogUtil.e("requestScreenCapture launcher and listener is null")
            return false
        }
        val mediaProjectionManager = activity.getSystemService(Context.MEDIA_PROJECTION_SERVICE) as MediaProjectionManager
        val captureIntent = mediaProjectionManager.createScreenCaptureIntent()
//        screenCaptureLauncher?.launch(captureIntent)
        activity.startActivityForResult(captureIntent, 100001)
        completableDeferredEnable = CompletableDeferred()

        return completableDeferredEnable?.await() ?: false
    }
    /**
     * @param path 保存的音频路径
     * @param audioSource 1:录制麦克风声音 2:内录，录制手机播放的声音
     */
    private var processor: AudioProcessor? = null
    @RequiresPermission(Manifest.permission.RECORD_AUDIO)
    suspend fun startDialog(activity: Activity? = null, path: String = "", audioSource: Int = 1,isRetry: Boolean = false): Boolean {

        LogUtil.d(TAG, "startDialog: start path: ${path} isRetry:$isRetry ")
        if (_recordStateFlow.value == RECORDING && !isRetry){
            LogUtil.d(TAG, "startDialog: 正在录音中： ${_recordStateFlow.value}")
            return false
        }
        recordType = type
        this.audioSource = audioSource
        isInitSpeex = false
        _recordStateFlow.value = PREPARE
        _isCancelStateFlow.value = false
        _audioRecordStopStateFlow.value = RecordInfoModel()
        audioDurationJob?.cancel()
        audioDurationJob = null
        mStopping = false
        isPause = false
        _isUserStopStateFlow.value = false
        if (!isRetry){
            _dialogContentStateFlow.value = "" to ""
            _audioDurationStateFlow.value = 0
            processor = null
        }
        // 使用自身的 AudioRecord 实现
        if (mAudioRecorder == null) {
            //录音初始化，录音参数中格式只支持16bit/单通道，采样率支持8K/16K

            LogUtil.d(TAG, "正在创建AudioRecord")
            if (audioSource == 1){
                mAudioRecorder = AudioRecord(MediaRecorder.AudioSource.VOICE_RECOGNITION,
                    SAMPLE_RATE,
                    AudioFormat.CHANNEL_IN_MONO,
                    AudioFormat.ENCODING_PCM_16BIT,
                    recordBufferSize)
            }else if (audioSource == 2){
                val mediaProjection = this.mediaProjection ?: return false

                val playbackConfig = AudioPlaybackCaptureConfiguration.Builder(mediaProjection)
                    .addMatchingUsage(AudioAttributes.USAGE_MEDIA) // 比如只录媒体声音
                    .addMatchingUsage(AudioAttributes.USAGE_GAME)          // 游戏声音
                    .build()
//
                mAudioRecorder = AudioRecord.Builder()
                    .setAudioFormat(
                        AudioFormat.Builder()
                            .setEncoding(AudioFormat.ENCODING_PCM_16BIT)
                            .setSampleRate(AliSpeechHelper.SAMPLE_RATE)
                            .setChannelMask(AudioFormat.CHANNEL_IN_MONO)
                            .build()
                    )
                    .setBufferSizeInBytes(AliSpeechHelper.recordBufferSize)
                    .setAudioPlaybackCaptureConfig(playbackConfig)
                    .build()
            }
            LogUtil.d(TAG, "AudioRecorder new ...")
        } else {
            LogUtil.w(TAG, "AudioRecord has been new ...")
        }
        
        if (mAudioRecorder?.state != AudioRecord.STATE_INITIALIZED) {
            LogUtil.e(TAG, "AudioRecord初始化失败，无法开始语音识别")
            mAudioRecorder?.release()
            mAudioRecorder = null
            _recordStateFlow.value = ERROR
            return false
        }
        mAudioRecorder?.let {
            LogUtil.e(TAG, "SpeexJNIBridge init thread:${Thread.currentThread().name}")
            if (NoiseSuppressor.isAvailable()) {
                val noiseSuppressor = NoiseSuppressor.create(it.audioSessionId)
                if (noiseSuppressor != null){
                    noiseSuppressor.enabled = true
                }
            }
        }


        val result = withContext(Dispatchers.IO) {
            //设置相关识别参数，具体参考API文档，在startDialog前调用
            val setParamsString = genParams()
            LogUtil.i(TAG, "nui set params $setParamsString")
            nui_instance?.setParams(setParamsString)
            //开始实时识别
            val ret = nui_instance?.startDialog(Constants.VadMode.TYPE_P2T,
                genDialogParams())?:-1
            LogUtil.i(TAG, "start done with $ret")
            if (ret == Constants.NuiResultCode.SUCCESS) {
                LogUtil.i(TAG, "点击<停止>结束实时识别")
                _recordStateFlow.value = RECORDING
                true
            }else{
                _recordStateFlow.value = ERROR
                false
            }
        }
        if (!result){
            return false
        }

        if (!isRetry){
            androidLame = null
            outputFile = null
            if (isSaveAudio && path.isNotBlank()){
                androidLame = LameBuilder()
                    .setInSampleRate(SAMPLE_RATE)
                    .setOutSampleRate(SAMPLE_RATE) // 确保输出采样率与输入相同
                    .setOutChannels(1)
                    .setOutBitrate(128) // 设置合理的比特率
                    .setQuality(5) // 设置质量 (0=最好/慢, 9=最差/快)
                    .setVbrMode(LameBuilder.VbrMode.VBR_OFF) // 使用更高质量的VBR模式
                    .setVbrQuality(4) // VBR质量设置 (0=最好/慢, 9=最差/快)
                    .build()
                outputFile = File(path)
                outputStream = outputFile?.outputStream()?.apply {
                    writeID3v2Tag(this)
                }
            }
        }
        return true
    }

    fun resume(){
        LogUtil.e(TAG, "resume")
        if (mStopping){
            return
        }
        if (_recordStateFlow.value != PAUSED){
            return
        }
        isPause = false
        _recordStateFlow.value = RECORDING
    }

    fun pause(){
        LogUtil.e(TAG, "pause")
        if (mStopping){
            return
        }
        if (_recordStateFlow.value != RECORDING){
            return
        }
        isPause = true
        _recordStateFlow.value = PAUSED
    }

    fun stopDialog(isCancel: Boolean = false, isUser: Boolean = false){
        LogUtil.e(TAG, "stopDialog isCancel:$isCancel isUser:$isUser mStopping:$mStopping outputFile:${outputFile?.path}")
        if (mStopping){
            return
        }
        _isUserStopStateFlow.value = true
        stopScreenCapture()
        _isCancelStateFlow.value = isCancel
        _recordStateFlow.value = STOPPED
        mStopping = true
        isPause = false
//        getSystemService(Context.AUDIO_SERVICE) as? AudioManager
//        // 请求短暂音频焦点
//        val result = audioManager.requestAudioFocus(
//            focusChangeListener,
//            AudioManager.STREAM_MUSIC,
//            AudioManager.AUDIOFOCUS_GAIN_TRANSIENT
//        )
        val name = outputFile?.name

        mainScope.launch(Dispatchers.IO) {
            val ret = nui_instance?.stopDialog()
            LogUtil.e(TAG, "stopDialog ret $ret")
        }
        if (isCancel){
            name?.substringBefore("_")?.let {
                MMKVHelper.removeRecording(it)
            }
        }
    }


    suspend fun stopDialogSuspend(isCancel: Boolean = false, isUser: Boolean = false){
        LogUtil.e(TAG, "stopDialog isCancel:$isCancel isUser:$isUser mStopping:$mStopping")
        if (mStopping){
            return
        }
        _isUserStopStateFlow.value = true
        stopScreenCapture()
        _isCancelStateFlow.value = isCancel
        _recordStateFlow.value = STOPPED
        mStopping = true
        isPause = false
//        getSystemService(Context.AUDIO_SERVICE) as? AudioManager
//        // 请求短暂音频焦点
//        val result = audioManager.requestAudioFocus(
//            focusChangeListener,
//            AudioManager.STREAM_MUSIC,
//            AudioManager.AUDIOFOCUS_GAIN_TRANSIENT
//        )
        if (isCancel){
            outputFile?.name?.substringBefore("_")?.let {
                MMKVHelper.removeRecording(it)
            }
        }

        withContext(Dispatchers.IO) {
            val ret = nui_instance?.stopDialog()
            LogUtil.e(TAG, "stopDialog ret $ret")
        }
    }


    fun getCurrentFile(): File?{
        return outputFile
    }

    private suspend fun onlyStopDialogSuspend(isCancel: Boolean = false, isUser: Boolean = false){
        LogUtil.e(TAG, "stopDialogSuspend $isCancel $isUser")
        if (mStopping){
            return
        }
        _isUserStopStateFlow.value = false
        val ret = nui_instance?.stopDialog()
        LogUtil.e(TAG, "stopDialogSuspend nui_instance ${nui_instance} $ret")
    }

    fun release(){
        LogUtil.e(TAG, "release")
        nui_instance?.release();
        nui_instance = null
    }

    private fun genParams(): String {
        var params = ""
        try {
            val nls_config = JSONObject()

            //参数可根据实际业务进行配置
            //接口说明可见https://help.aliyun.com/document_detail/173528.html
            //查看 2.开始识别

            // 是否返回中间识别结果，默认值：False。
            nls_config.put("enable_intermediate_result", true)
            // 是否在后处理中添加标点，默认值：False。
            nls_config.put("enable_punctuation_prediction", true)

            nls_config.put("sample_rate", SAMPLE_RATE)
            nls_config.put("sr_format", AUDIO_FORMAT)
//      nls_config.put("enable_inverse_text_normalization", true)
//      nls_config.put("max_sentence_silence", 800)
//      nls_config.put("enable_words", false)

            /*若文档中不包含某些参数，但是此功能支持这个参数，可以用如下万能接口设置参数*/
//      val extend_config = JSONObject()
//      extend_config.put("custom_test", true)
//      nls_config.put("extend_config", extend_config)

            val tmp = JSONObject()
            tmp.put("nls_config", nls_config)
            tmp.put("service_type", Constants.kServiceTypeSpeechTranscriber) // 必填

//      如果有HttpDns则可进行设置
//      tmp.put("direct_ip", Utils.getDirectIp())

            params = tmp.toString()
        } catch (e: JSONException) {
            e.printStackTrace()
        }
        return params
    }


    private fun genInitParams(workpath: String, debug_path: String): String {
        var str = ""
        try {
            // 获取账号访问凭证：
            var method = Auth.GetTicketMethod.GET_TOKEN_FROM_SERVER_FOR_ONLINE_FEATURES
            if (g_appkey.isNotEmpty()) {
                Auth.setAppKey(g_appkey)
            }
            if (g_token.isNotEmpty()) {
                Auth.setToken(g_token)
            }
            if (g_ak.isNotEmpty()) {
                Auth.setAccessKey(g_ak)
            }
            if (g_sk.isNotEmpty()) {
                Auth.setAccessKeySecret(g_sk)
            }
            Auth.setStsToken(g_sts_token)
//             此处展示将用户传入账号信息进行交互，实际产品不可以将任何账号信息存储在端侧
            if (g_appkey.isNotEmpty()) {
                if (g_ak.isNotEmpty() && g_sk.isNotEmpty()) {
                    if (g_sts_token.isEmpty()) {
                        method = Auth.GetTicketMethod.GET_ACCESS_IN_CLIENT_FOR_ONLINE_FEATURES
                    } else {
                        method = Auth.GetTicketMethod.GET_STS_ACCESS_IN_CLIENT_FOR_ONLINE_FEATURES
                    }
                }
                if (g_token.isNotEmpty()) {
                    method = Auth.GetTicketMethod.GET_TOKEN_IN_CLIENT_FOR_ONLINE_FEATURES
                }
            }
            LogUtil.i(TAG, "Use method: $method")
            val obj = Auth.getTicket(method)
            if (!obj.containsKey("token")) {
                LogUtil.e(TAG, "Cannot get token !!!")
            }

            obj.put("device_id", "empty_device_id") // 必填, 推荐填入具有唯一性的id, 方便定位问题
            if (g_url.isEmpty()) {
                g_url = "wss://nls-gateway.cn-shanghai.aliyuncs.com:443/ws/v1" // 默认
            }
            obj.put("url", g_url)

            // 工作目录路径，SDK从该路径读取配置文件
            // object.put("workspace", workpath) // V2.6.2版本开始纯云端功能可不设置workspace

            // 当初始化SDK时的save_log参数取值为true时，该参数生效。表示是否保存音频debug，该数据保存在debug目录中，需要确保debug_path有效可写。
            obj.put("save_wav", "true")
            // debug目录，当初始化SDK时的save_log参数取值为true时，该目录用于保存中间音频文件。
            obj.put("debug_path", debug_path)

            // FullMix = 0   // 选用此模式开启本地功能并需要进行鉴权注册
            // FullCloud = 1
            // FullLocal = 2 // 选用此模式开启本地功能并需要进行鉴权注册
            // AsrMix = 3    // 选用此模式开启本地功能并需要进行鉴权注册
            // AsrCloud = 4
            // AsrLocal = 5  // 选用此模式开启本地功能并需要进行鉴权注册
            // 这里只能选择FullMix和FullCloud
            obj.put("service_mode", Constants.ModeFullCloud) // 必填
            str = obj.toString()
        } catch (e: JSONException) {
            e.printStackTrace()
        }

        // 注意! str中包含ak_id ak_secret token app_key等敏感信息, 实际产品中请勿在Log中输出这类信息！
        LogUtil.i(TAG, "InsideUserContext: $str")
        return str
    }

    private fun genDialogParams(): String {
        var params = ""
        try {
            var dialog_param = JSONObject()
            // 运行过程中可以在startDialog时更新临时参数，尤其是更新过期token
            // 注意: 若下一轮对话不再设置参数，则继续使用初始化时传入的参数
            val distance_expire_time_30m = 1800L
            dialog_param = Auth.refreshTokenIfNeed(dialog_param, distance_expire_time_30m)

            // 注意: 若需要更换appkey和token，可以直接传入参数
//      dialog_param.put("app_key", "")
//      dialog_param.put("token", "")
            params = dialog_param.toString()
        } catch (e: JSONException) {
            e.printStackTrace()
        }

        LogUtil.i(TAG, "dialog params: $params")
        return params
    }



    fun writeRecordToMP3(readSize: Int, buffer: ByteArray){
        if (readSize <= 0) {
            return
        }
        val androidLame = this.androidLame ?: return
        try {

            // 为立体声录制创建空的右声道数组
            val emptyRightChannel = if (channelCount == 2) ShortArray(buffer.size / 2) else shortArrayOf(0)
            if (mp3Buffer == null){
                mp3Buffer = ByteArray((7200 + readSize * 1.25).toInt())
            }
            outputStream?.let { fos ->
                val pcmBuffer = byteArrayToShortArray(buffer)
//                 编码为MP3 - 安全处理单声道和立体声情况
                val encodedSize = if (channelCount == 1) {
                    // 单声道编码 - 第二个参数传null
                    androidLame.encode(pcmBuffer, shortArrayOf(0), readSize / 2, mp3Buffer)
                } else {
                    // 立体声编码 - 需要分离左右声道
                    androidLame.encode(pcmBuffer, emptyRightChannel, readSize / 4, mp3Buffer)
                }
                if (encodedSize > 0) {
                    fos.write(mp3Buffer, 0, encodedSize)
                }
            }
        } catch (e: Exception) {
            LogUtil.e("startRecordToMP3 recording error: ${e.message}")
            e.printStackTrace()
        } finally {

        }
    }

    private fun writeOutputFinish(){
        // 完成编码，写入最后的MP3帧
        audioDurationJob?.cancel()
        audioDurationJob = null
        if (!_isUserStopStateFlow.value){
            return
        }
        try {
            if (outputStream == null) return
            val androidLame = androidLame ?: return
            outputStream?.let { fos->
                var flushSize: Int
                do {
                    flushSize = androidLame.flush(mp3Buffer)
                    if (flushSize > 0) {
                        fos.write(mp3Buffer, 0, flushSize)
                    }
                } while (flushSize > 0)

                // 写入ID3v1标签
                writeID3v1Tag(fos)
                AudioUtils.writeID3v1Tag(fos)
                AudioUtils.writeID3v1Tag(fos)
                // 关闭LAME编码器
                androidLame.close()
                this.androidLame = null
            }
            outputStream?.close()
            outputStream = null
            SpeexJNIBridge.destory()
            LogUtil.e("$TAG writeOutputFinish _isCancelStateFlow: ${_isCancelStateFlow.value} file:${outputFile?.absolutePath}")
            if (_isUserStopStateFlow.value && !_isCancelStateFlow.value){
                _audioRecordStopStateFlow.value = RecordInfoModel(path = outputFile?.absolutePath?:"", duration = _audioDurationStateFlow.value)
                LogUtil.e("$TAG writeOutputFinish _audioRecordStopStateFlow: ${_audioRecordStopStateFlow.value}")
            }else{
                _audioRecordStopStateFlow.value = RecordInfoModel(path = "cancel", duration = _audioDurationStateFlow.value)
            }
            outputFile = null
        } catch (e: Exception) {
            LogUtil.e("writeOutputFinish catch: ${e.message}")
        }
    }
    private fun byteArrayToShortArray(byteArray: ByteArray): ShortArray {
        require(byteArray.size % 2 == 0) { "Byte array length must be even" }
        val shortBuffer = ByteBuffer.wrap(byteArray).order(ByteOrder.LITTLE_ENDIAN).asShortBuffer()
        return ShortArray(shortBuffer.remaining()).also { shortBuffer.get(it) }
    }


    /**
     * 写入ID3v2标签到MP3文件
     */
    private fun writeID3v2Tag(outputStream: OutputStream) {
        try {
            // ID3v2标签头
            val header = ByteArray(10)
            header[0] = 'I'.code.toByte()
            header[1] = 'D'.code.toByte()
            header[2] = '3'.code.toByte()
            header[3] = 3  // 版本2.3
            header[4] = 0  // 修订版本
            header[5] = 0  // 标志

            // 标签大小 (不包括头10字节) - 设置为0，简单起见
            header[6] = 0
            header[7] = 0
            header[8] = 0
            header[9] = 0

            outputStream.write(header)
        } catch (e: IOException) {
            e.printStackTrace()
        }
    }

    /**
     * 写入ID3v1标签到MP3文件
     */
    private fun writeID3v1Tag(outputStream: OutputStream) {
        try {
            val tag = ByteArray(128)
            tag[0] = 'T'.code.toByte()
            tag[1] = 'A'.code.toByte()
            tag[2] = 'G'.code.toByte()

            // 标题 (30字节)
            val title = "Recorded Audio"
            System.arraycopy(title.toByteArray(), 0, tag, 3, Math.min(title.length, 30))

            // 艺术家 (30字节)
            val artist = "App Recording"
            System.arraycopy(artist.toByteArray(), 0, tag, 33, Math.min(artist.length, 30))

            // 专辑 (30字节)
            val album = "App Recordings"
            System.arraycopy(album.toByteArray(), 0, tag, 63, Math.min(album.length, 30))

            // 年份 (4字节)
            val year = java.util.Calendar.getInstance().get(java.util.Calendar.YEAR).toString()
            System.arraycopy(year.toByteArray(), 0, tag, 93, Math.min(year.length, 4))

            // 注释 (28字节) + 0 + 曲目号
            val comment = "Recorded with AndroidLame"
            System.arraycopy(comment.toByteArray(), 0, tag, 97, Math.min(comment.length, 28))

            // 流派 (1字节)
            tag[127] = 0  // 未知流派

            outputStream.write(tag)
        } catch (e: IOException) {
            e.printStackTrace()
        }
    }

    /**
     * 启用或禁用后台语音识别模式
     */
    fun enableBackgroundRecognition(enable: Boolean) {
        isBackgroundModeEnabled = enable
        LogUtil.i(TAG, "后台语音识别模式已${if (enable) "启用" else "禁用"}")
        
        // 如果是启用后台模式，可以在这里进行一些优化配置
//        if (enable) {
//            // 例如，可以调整VAD参数，使识别更加连续
//            try {
//                nui_instance?.setParams("{\"vad.long_wait\":\"1\"}") // 设置较长的静音等待时间
//                LogUtil.i(TAG, "已为后台模式优化VAD参数")
//            } catch (e: Exception) {
//                LogUtil.e(TAG, "设置后台模式VAD参数出错: ${e.message}")
//            }
//        } else {
//            // 恢复普通模式的参数
//            try {
//                nui_instance?.setParams("{\"vad.long_wait\":\"0\"}") // 恢复默认静音等待时间
//                LogUtil.i(TAG, "已恢复普通模式VAD参数")
//            } catch (e: Exception) {
//                LogUtil.e(TAG, "恢复普通模式VAD参数出错: ${e.message}")
//            }
//        }
    }
    
    /**
     * 检查是否处于后台模式
     */
    fun isBackgroundModeEnabled(): Boolean {
        return isBackgroundModeEnabled
    }

}