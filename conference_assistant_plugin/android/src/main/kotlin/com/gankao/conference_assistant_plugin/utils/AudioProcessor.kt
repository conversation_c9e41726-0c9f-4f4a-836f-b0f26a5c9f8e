package com.gankao.conference_assistant_plugin.utils

import com.gk.logcat.LogUtil


class AudioProcessor(val frameSize: Int) {
    private val frameByteSize = frameSize // short = 2 bytes
    private val buffer = ByteArray(4096)
    private var bufferOffset = 0

    fun processAudio(input: ByteArray, onFrameReady: (ByteArray) -> Unit) {
        var offset = 0
        while (offset < input.size) {
            val remaining = input.size - offset
            val copyLen = minOf(buffer.size - bufferOffset, remaining)
            System.arraycopy(input, offset, buffer, bufferOffset, copyLen)
            bufferOffset += copyLen
            offset += copyLen

            // Enough for one frame
//            LogUtil.e("AudioProcessor processAudio: bufferOffset: $bufferOffset  $frameByteSize")
            while (bufferOffset >= frameByteSize) {
                val frame = ByteArray(frameByteSize)
                System.arraycopy(buffer, 0, frame, 0, frameByteSize)
                onFrameReady(frame) // send to JNI

                // shift remaining bytes
                val leftover = bufferOffset - frameByteSize
                System.arraycopy(buffer, frameByteSize, buffer, 0, leftover)
                bufferOffset = leftover
            }
        }
    }
}