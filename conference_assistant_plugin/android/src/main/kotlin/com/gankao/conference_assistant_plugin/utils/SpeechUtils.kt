package com.gankao.conference_assistant_plugin.utils

import android.Manifest
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.os.PowerManager
import androidx.core.app.ActivityCompat
import com.gankao.conference_assistant_plugin.AlertActivity
import com.gankao.conference_assistant_plugin.speech.AliSpeechHelper
import com.gk.logcat.LogUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext
import kotlin.jvm.java

object SpeechUtils {





    suspend fun showAlertActivity(context: Context) {
        val powerManager = context.getSystemService(Context.POWER_SERVICE) as PowerManager
        LogUtil.e("SpeechUtils showAlertActivity")
        // 唤醒屏幕
        if (!powerManager.isInteractive) {
            val wakeLock = powerManager.newWakeLock(
                PowerManager.SCREEN_BRIGHT_WAKE_LOCK or PowerManager.ACQUIRE_CAUSES_WAKEUP,
                "MyApp::WakeLock"
            )
            wakeLock.acquire(3000) // 最多点亮屏幕3秒，防止资源泄漏
        }
        withContext (Dispatchers.IO){
            delay(500)
        }

        if (ActivityCompat.checkSelfPermission(
                context,
                Manifest.permission.POST_NOTIFICATIONS
            ) == PackageManager.PERMISSION_GRANTED
        ) {
            NotificationHelper.showNotification(context, "还在吗？","超过60秒没有听到人声，将自动暂停，以节约资源")
        }

        AlertActivity.shouldShow = true
        // 启动弹窗页面
        val intent = Intent(context, AlertActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
        }
        context.startActivity(intent)

        withContext(Dispatchers.IO){
            delay(500)
            (10 downTo 0)
                .forEach {
                    delay(1000)
                    LogUtil.e("showAlertActivity ${it}s后结束录音，shouldShow:${AlertActivity.shouldShow}")
                    if (it == 0){
                        withContext(Dispatchers.Main) {
                            // 停止录音
//                            LogUtil.e("showAlertActivity ${it}s后结束录音，shouldShow:${AlertActivity.shouldShow}")
                            if (AlertActivity.shouldShow && AliSpeechHelper.recordStateFlow.value == AliSpeechHelper.RECORDING) {
                                AlertActivity.shouldShow = false
                                AliSpeechHelper.stopDialogSuspend(isUser = true)
                                if (ActivityCompat.checkSelfPermission(
                                        context,
                                        Manifest.permission.POST_NOTIFICATIONS
                                    ) == PackageManager.PERMISSION_GRANTED
                                ) {
                                    NotificationHelper.showNotification(context, "提示","会议静音超过60秒，AI助理自动结束！")
                                }
                                AlertActivity.instance?.finish()
                            }
                        }
                    }
                }


        }

    }


}