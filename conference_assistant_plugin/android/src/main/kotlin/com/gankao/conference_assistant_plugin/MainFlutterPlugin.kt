package com.gankao.conference_assistant_plugin

import android.os.Build
import androidx.annotation.NonNull
import com.gankao.conference_assistant_plugin.material.MaterialLibraryPlugin
import com.gankao.conference_assistant_plugin.ConferenceAssistantPlugin
import com.gankao.conference_assistant_plugin.speech.AliSpeechHelper
import com.gk.logcat.LogUtil
import io.flutter.embedding.engine.plugins.FlutterPlugin
import io.flutter.embedding.engine.plugins.activity.ActivityAware
import io.flutter.embedding.engine.plugins.activity.ActivityPluginBinding


private const val TAG = "MainFlutterPlugin"
class MainFlutterPlugin: FlutterPlugin, ActivityAware{
    private val conferenceAssistantPlugin = ConferenceAssistantPlugin()
    private val materialLibraryPlugin = MaterialLibraryPlugin()

    override fun onAttachedToEngine(@NonNull flutterPluginBinding: FlutterPlugin.FlutterPluginBinding) {
        LogUtil.i(TAG, "MainFlutterPlugin attached to engine")
        conferenceAssistantPlugin.onAttachedToEngine(flutterPluginBinding)
        materialLibraryPlugin.onAttachedToEngine(flutterPluginBinding)
    }

    override fun onDetachedFromEngine(@NonNull binding: FlutterPlugin.FlutterPluginBinding) {
        LogUtil.i(TAG, "MainFlutterPlugin detached from engine")
        conferenceAssistantPlugin.onDetachedFromEngine(binding)
        materialLibraryPlugin.onDetachedFromEngine(binding)
    }

    override fun onAttachedToActivity(binding: ActivityPluginBinding) {
        LogUtil.e(TAG, "onAttachedToActivity:$binding")
        conferenceAssistantPlugin.onAttachedToActivity(binding)
    }

    override fun onDetachedFromActivity() {
        LogUtil.e(TAG, "onDetachedFromActivity")
        conferenceAssistantPlugin.onDetachedFromActivity()
    }

    override fun onReattachedToActivityForConfigChanges(binding: ActivityPluginBinding) {
        LogUtil.e(TAG, "onReattachedToActivityForConfigChanges:$binding")
        conferenceAssistantPlugin.onReattachedToActivityForConfigChanges(binding)
    }

    override fun onDetachedFromActivityForConfigChanges() {
        LogUtil.e(TAG, "onDetachedFromActivityForConfigChanges")
        conferenceAssistantPlugin.onDetachedFromActivityForConfigChanges()
    }
}