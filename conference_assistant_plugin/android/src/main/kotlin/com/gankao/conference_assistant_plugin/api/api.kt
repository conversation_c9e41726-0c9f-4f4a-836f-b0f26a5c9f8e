package com.gankao.conference_assistant_plugin.api

import com.gankao.common.http.RetrofitHelper
import com.gankao.common.http.getService
import com.gankao.common.http.getServiceV3
import com.gankao.conference_assistant_plugin.model.UploadModel
import com.gankao.network.http.result.HttpDataResult
import com.gankao.network.http.result.HttpSimpleResult
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.HeaderMap
import retrofit2.http.Headers
import retrofit2.http.POST


object Api{

    const val URL_TEST = "https://api.gankaotest2.com"
    val service by lazy {
        RetrofitHelper.getGKApi().getService<ApiInterface>("https://api.gankaotest2.com")
    }
    val serviceMap = mutableMapOf<String,ApiInterface>()

    fun getService(url: String = URL_TEST): ApiInterface {
        var service = serviceMap[url]
        if (service == null) {
            service = RetrofitHelper.getGKApi().getService<ApiInterface>(url)
            serviceMap[url] = service
        }
        return service
    }



}


interface ApiInterface{
    @GET("api-recite/gkaicard/getQiniuToken")
//    @Headers("Authorization:11189913,3b78c0d20352bce190c045c3711c1d77,nihao")
    suspend fun getQiniuToken(
        @HeaderMap header: MutableMap<String, String> = mutableMapOf()): HttpSimpleResult<UploadModel>


    @POST("api-recite/gkrecording/uploadRecording")
//    @Headers("Authorization:11189913,3b78c0d20352bce190c045c3711c1d77,nihao")
    suspend fun uploadRecordingInfo(@Body body: Any, @HeaderMap header: MutableMap<String, String> = mutableMapOf()): HttpSimpleResult<Any?>

//    https://api.gankaotest2.com/api-recite/gkguest/aliyun_nls_getToken
    @POST("api-recite/gkguest/aliyun_nls_getToken")
    @Headers("temp2:lkjOIU76675^%#@756aq1")
    suspend fun getAliyunNlsToken(@HeaderMap header: MutableMap<String, String> = mutableMapOf()): HttpSimpleResult<UploadModel?>


    @POST("api-recite/gkrecording/updateRecordingContent")
    suspend fun updateRecordingContent(@Body body: Any, @HeaderMap header: MutableMap<String, String> = mutableMapOf()): HttpSimpleResult<Any?>

}