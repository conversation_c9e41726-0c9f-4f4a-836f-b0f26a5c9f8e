package com.gankao.conference_assistant_plugin.utils

import android.content.Context
import android.content.Intent

object NativeBridge {

    fun getFlutterIntentViaReflection(context: Context, route: String): Intent? {
        return try {
            val clazz = Class.forName("com.gankao.junior.cutflow.flutter.FlutterBridge")
            val method = clazz.getMethod("buildFlutterIntent", Context::class.java, String::class.java)
            method.invoke(null, context, route) as? Intent
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }

    fun invokeNativeMethod(methodName: String, params: String ): Any? {
        return try {
            val clazz = Class.forName("com.gankao.junior.cutflow.flutter.FlutterBridge")
            val method = clazz.getMethod(methodName,  String::class.java)
            method.invoke(null, params)
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }

}