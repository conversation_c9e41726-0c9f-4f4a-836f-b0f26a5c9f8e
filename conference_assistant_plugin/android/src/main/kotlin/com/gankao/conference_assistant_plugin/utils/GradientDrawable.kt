package com.gankao.conference_assistant_plugin.utils



import android.content.Context
import android.content.res.ColorStateList
import android.content.res.Resources
import android.graphics.drawable.Drawable
import android.graphics.drawable.GradientDrawable
import android.graphics.drawable.GradientDrawable.OVAL
import android.graphics.drawable.RippleDrawable
import android.os.Build
import android.util.TypedValue
import androidx.annotation.ColorInt
import androidx.annotation.ColorRes
import androidx.core.content.ContextCompat


val Float.dp2px
    get() = TypedValue.applyDimension(
        TypedValue.COMPLEX_UNIT_DIP,
        this,
        Resources.getSystem().displayMetrics
    )


fun GradientDrawable.buildCircle(): GradientDrawable {
    this.shape = OVAL
    return this
}

fun GradientDrawable.buildStroke(px: Int, @ColorInt color: Int): GradientDrawable {
    this.setStroke(px, color)
    return this
}

fun GradientDrawable.buildStrokeDp(dp: Float,@ColorInt color: Int): GradientDrawable {
    this.setStroke(dp.dp2px.toInt(), color)
    return this
}

fun GradientDrawable.buildSolid(@ColorInt color: Int): GradientDrawable {
    this.setColor(color)
    return this
}

fun GradientDrawable.buildCorner(cornerPx: Float): GradientDrawable {
    this.cornerRadius = cornerPx
    return this
}

fun GradientDrawable.buildCornerDp(cornerDp: Float): GradientDrawable {
    this.cornerRadius = cornerDp.dp2px
    return this
}

fun GradientDrawable.buildCorner(
    leftTop: Float = 0f,
    rightTop: Float = 0f,
    rightBottom: Float = 0f,
    leftBottom: Float = 0f
): GradientDrawable {
    this.cornerRadii = floatArrayOf(
        leftTop,
        leftTop,
        rightTop,
        rightTop,
        rightBottom,
        rightBottom,
        leftBottom,
        leftBottom
    )
    return this
}

fun GradientDrawable.buildCornerDp(
    leftTopDp: Float = 0f,
    rightTopDp: Float = 0f,
    rightBottomDp: Float = 0f,
    leftBottomDp: Float = 0f
): GradientDrawable {
    val leftTop = leftTopDp.dp2px
    val rightTop = rightTopDp.dp2px
    val rightBottom = rightBottomDp.dp2px
    val leftBottom = leftBottomDp.dp2px
    this.cornerRadii = floatArrayOf(
        leftTop, leftTop, rightTop, rightTop, rightBottom, rightBottom, leftBottom, leftBottom
    )
    return this
}

fun buildRippleDrawable(
    @ColorInt rippleColor: Int,
    backgroundDrawable: GradientDrawable
): Drawable? {
    return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
        RippleDrawable(
            ColorStateList.valueOf(rippleColor),backgroundDrawable, null
        )
    } else {
        backgroundDrawable
    }
}

fun GradientDrawableColorsArray(context: Context, @ColorRes intArray: IntArray, orientation:GradientDrawable.Orientation=GradientDrawable.Orientation.LEFT_RIGHT):GradientDrawable{
    val colorArray=intArray.map { ContextCompat.getColor(context,it) }.toIntArray()
    return GradientDrawable(orientation,colorArray)
}