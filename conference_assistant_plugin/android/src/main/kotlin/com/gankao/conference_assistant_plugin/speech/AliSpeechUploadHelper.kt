package com.gankao.conference_assistant_plugin.speech

import android.R.attr.duration
import android.R.attr.path
import android.R.attr.type
import android.content.Context
import android.net.Uri
import android.os.Environment
import com.alibaba.fastjson.JSONObject
import com.gankao.conference_assistant_plugin.UploadStatusStreamHandler
import com.gankao.conference_assistant_plugin.api.Api
import com.gankao.conference_assistant_plugin.api.Api.URL_TEST
import com.gankao.conference_assistant_plugin.utils.AudioUtils
import com.gankao.conference_assistant_plugin.utils.MMKVHelper
import com.gankao.conference_assistant_plugin.utils.QiNiuHelper
import com.gankao.conference_assistant_plugin.utils.sendNativeParams
import com.gankao.network.http.request
import com.gk.logcat.LogUtil
import com.gk.logcat.LogUtil.context
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.delay
import java.io.File
import java.io.FileOutputStream
import kotlin.collections.set

private const val TAG = "AliSpeechUploadHelper"
object AliSpeechUploadHelper {


    suspend fun getTokenAndUploadFileToAliSpeech(context: Context,
                                                 url: String = URL_TEST,
                                                 record: RecordInfoModel, name: String,
                                                 headerMap: MutableMap<String, String> = mutableMapOf(),
                                                 onProgress:((Double)->Unit)? = null): String?{
        return runCatching {
            val token = request { Api.getService(url).getQiniuToken(headerMap) }?.token
            LogUtil.e("token: $token")
            if (token.isNullOrBlank()){
                return null
            }
            val result = if (record.path.startsWith("content://") || record.path.startsWith("file://")){
                val uri = Uri.parse(record.path)
                QiNiuHelper.uploadUriToQiniuSuspend(context, token, uri, name, onProgress = {
                    onProgress?.invoke(it)
                })
            }else{
                val file = File(record.path)
                QiNiuHelper.uploadFileToQiniuSuspend(token, file, name, onProgress = {
                    onProgress?.invoke(it)
                })
            }

            LogUtil.e("uploadFiles success: ${name}   ${result?.response}")
            val key = result?.response?.optString("key")
            val url = "https://gkwords.qiaoxuesi.com/$key"
            if (key.isNullOrBlank()){
                null
            }else{
                url
            }

//            val duration = record.duration
//            val content = AliSpeechHelper.dialogContentStateFlow.value
//            LogUtil.e("uploadFiles success  content: ${content}")
//            val body = mutableMapOf<String, Any>()
//            body["name"] = name
//            body["file_url"] = url
//            body["file_duration"] = duration
//            body["tapescript"] = content.second
//            request { Api.service.uploadRecordingInfo(body) }
        }.getOrNull()
    }


    suspend fun uploadLocalFileAndMeeting(context: Context, baseUrl: String, headerMap: MutableMap<String, String>, uploadingRecordings: List<Map<String, Any>>, uploadStatusStreamHandler: UploadStatusStreamHandler?){
        val list = MMKVHelper.getAllRecordings().toMutableList()
        LogUtil.e(TAG, "uploadLocalFileAndMeeting  start:${System.currentTimeMillis()}  list: $list。 uploadingRecordings: $uploadingRecordings")
        if (list.isEmpty() && uploadingRecordings.isEmpty()){
            return
        }
        uploadStatusStreamHandler?.sendUploadStatus(mutableMapOf<String, Any>().apply {
            this["upload_size"] = list.size
        })

        
        context.sendNativeParams("upload_start")
        val fileDir = context.filesDir?.listFiles() ?:arrayOf()

        val externalFilesDir = File(Environment.getExternalStorageDirectory().absolutePath +"/cutflow_audio")?.listFiles() ?: arrayOf()
        val downloadDir = File(Environment.getExternalStoragePublicDirectory("Recordings").absolutePath+"/cutflow_audio")?.listFiles() ?: arrayOf()

        val filesList = fileDir.plus(downloadDir).plus(externalFilesDir)
        LogUtil.e(TAG, "uploadLocalFileAndMeeting filesList: ${filesList.map { it.name }}")
        val realList = list.map { JSONObject.parseObject(it, Map::class.java) as? Map<String, Any> }.toMutableList()
        // realList中有的uuid uploadingRecordings 就不要添加进去了
        realList.addAll(uploadingRecordings)
        val uuidUploaded = mutableListOf<String>()
        var uploadNum = 0
        realList.forEach { map ->
//            val map = JSONObject.parseObject(it, Map::class.java) as? Map<String, Any>
            if (map == null){
                return@forEach
            }
            val uuid = map?.get("uuid") as? String ?:""
            if (uuid.isNullOrBlank() || uuidUploaded.contains(uuid)){
                LogUtil.e(TAG, "uploadLocalFileAndMeeting uuid.isNullOrBlank or contains")
                return@forEach
            }
            val typeId = map?.get("typeId") as? String ?: ""
            val type = (map?.get("type") as? String)?.takeIf { it.isNotBlank() } ?: typeId.takeIf { it.isNotBlank() } ?: "1"

            val title = map?.get("title") as? String ?: ""
            val name = (map?.get("name") as? String)?.takeIf { it.isNotBlank() } ?: title.takeIf { it.isNotBlank() } ?: ""

            val durationInSeconds = map?.get("durationInSeconds") as? String ?:""
            LogUtil.e(TAG, "uploadLocalFileAndMeeting uuid: $uuid  name:$name type:$type")

            if (name.isBlank()){
                return@forEach
            }
            var isCurrentRecording = false
            if (AliSpeechHelper.recordStateFlow.value == AliSpeechHelper.RECORDING){
                if (AliSpeechHelper.getCurrentFile()?.name?.substringBefore("_") == uuid){
                    isCurrentRecording = true
                }
            }
            LogUtil.e(TAG, "uploadLocalFileAndMeeting uuid: $uuid  isCurrentRecording:$isCurrentRecording type:$type")
            if (!isCurrentRecording && uuid.isNotEmpty()){
                filesList.firstOrNull { it.name.startsWith(uuid) }?.let { file->
                    LogUtil.e(TAG, "uploadLocalFileAndMeeting 找到文件: ${file.name}  exists:${file.exists()}")
                    if (!file.exists()){
                        return@let
                    }
                    FileOutputStream(file, true).use {
                        AudioUtils.writeID3v1Tag(it)
                    }

//                    'uuid': uuid,
//                    'tapescript': tapescript,
//                    'is_empty': isEmpty,
//                    'type': type,
//                    'name': name,
//                    'file_duration': fileDuration,

                    val duration = MMKVHelper.getRecordingDuration(uuid).let { if (it==0) durationInSeconds else it }
                    val body = mutableMapOf<String, Any>()
                    body["uuid"] = uuid
                    body["name"] = name
                    body["type"] = type
                    body["is_empty"] = 1
                    body["file_duration"] = duration
                    val resultContent = request {  Api.getService(baseUrl).updateRecordingContent(body, headerMap)  }
                    if (resultContent != null){
                        LogUtil.e(TAG, "updateRecordingContent success: $resultContent")

                        val url = repeatUpload(context, baseUrl, RecordInfoModel(path = file.path), name, headerMap)
                        if (url.isNullOrBlank()){
                            LogUtil.e(TAG, "getTokenAndUploadFileToAliSpeech: url is null or blank")
                        }else{
                            LogUtil.e( TAG, "getTokenAndUploadFileToAliSpeech url: $url")
//                        val duration = it.duration
                            val bodyInfo = mutableMapOf<String, Any>()
                            bodyInfo["name"] = name
                            bodyInfo["uuid"] = uuid
                            bodyInfo["type"] = type
                            bodyInfo["file_url"] = url
                            bodyInfo["file_duration"] = duration
//                        body["tapescript"] = content.second
                            val result = request { Api.getService(baseUrl).uploadRecordingInfo(bodyInfo, headerMap) }
                            LogUtil.e(TAG, "uploadRecordingInfo success : result: ${result}")
                            if (result != null){
                                LogUtil.e(TAG, "uploadRecordingInfo success : 上传成功，删除本地文件 ${file.path}")
                                uploadNum++
                                runCatching {
                                    uuidUploaded.add(uuid)
                                    file.delete()
                                    MMKVHelper.removeRecording(uuid)
                                }
                            }
                        }
                    }
                }?: run {
                    LogUtil.e(TAG, "uploadLocalFileAndMeeting 没有找到文件: ${uuid}")
                    MMKVHelper.removeRecording(uuid)
                }
            }
        }
        LogUtil.e(TAG, "uploadLocalFileAndMeeting  end:${System.currentTimeMillis()} ")

        delay(3000)
        uploadStatusStreamHandler?.sendUploadStatus(mutableMapOf<String, Any>().apply {
            this["upload_finish"] = 1
            this["refresh_list"] = if (uploadNum > 0) 1 else 0
        })
        context.sendNativeParams("upload_finish")
    }

    fun getUploadName(name: String): String {
        val formatName = name.replace(Regex("[^a-zA-Z0-9_.]"),"") //name.replace(Regex("[\u4e00-\u9fa5]"), "").replace(" ","")
        val uploadName = if (formatName.contains(".")){
            val dotIndex = formatName.lastIndexOf('.')
            Uri.encode("${formatName.substring(0, dotIndex)}_${System.currentTimeMillis()}${formatName.substring(dotIndex)}")
//      "aaa.mp3"
        }else{
            Uri.encode("${formatName}_${System.currentTimeMillis()}")
//    "bbbb.mp3"
        }
        return uploadName
    }

    suspend fun repeatUpload(context: Context, baseUrl: String, record: RecordInfoModel, name: String, headerMap: MutableMap<String, String>): String?{
        val repeatCount = 5
        var url: String? = null
        var repeat = 0
        while (url.isNullOrBlank() && repeat < repeatCount){
            LogUtil.e(TAG, "repeatUpload: 第 ${repeat + 1} 次上传")
            val uploadName = getUploadName(name)
            url = getTokenAndUploadFileToAliSpeech(context, baseUrl, record, uploadName, headerMap)
            if (url.isNullOrBlank()){
                LogUtil.e(TAG, "getTokenAndUploadFileToAliSpeech 返回的url为空，等待0.5秒后重试")
                delay(500)
            }
            repeat++
        }
        return url
    }

}