package com.gankao.conference_assistant_plugin.utils


import android.content.Context
import android.util.Log
import com.gankao.conference_assistant_plugin.utils.SpeechUtils.showAlertActivity
import com.gk.logcat.LogUtil
import kotlinx.coroutines.*
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.*

class DebouncedMessageWatcher(
    private val mContext: Context,
) {
    private val scope = CoroutineScope(Dispatchers.Main + SupervisorJob())
    private val messageChannel = Channel<Boolean>(Channel.UNLIMITED)

    private var debouncedJob: Job? = null
    fun start(timeoutMillis: Long = 60_000L) {
        LogUtil.e("DebouncedMessageWatcher  start: $timeoutMillis")
        stop()
        debouncedJob = scope.launch {
            while (isActive) {
                val timeout = withTimeoutOrNull(timeoutMillis) {
                    messageChannel.receive() // 若 timeoutMillis 内有消息，重置等待
                }
                if (timeout == null) {
                    // 超时触发
                    onTimeout()
                }
                // else: 收到消息就重新开始 while 循环
            }
        }
    }

    fun onMessageReceived() {
        // 每次收到消息就推入 Channel，触发 debounce 逻辑
        Log.e("DebouncedMessageWatcher", "onMessageReceived: " )
        messageChannel.trySend(true)
    }

    fun stop() {
        debouncedJob?.cancel()
        debouncedJob = null
    }

    private suspend fun onTimeout(){
        LogUtil.e("DebouncedMessageWatcher  onTimeout")
        // 超时处理逻辑
        showAlertActivity(mContext)
    }
}