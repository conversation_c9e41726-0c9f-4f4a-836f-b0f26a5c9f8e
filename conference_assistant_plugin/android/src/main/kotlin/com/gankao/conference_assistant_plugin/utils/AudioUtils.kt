package com.gankao.conference_assistant_plugin.utils

import java.io.IOException
import java.io.OutputStream

object AudioUtils {

    fun writeID3v1Tag(outputStream: OutputStream) {
        try {
            val tag = ByteArray(128)
            tag[0] = 'T'.code.toByte()
            tag[1] = 'A'.code.toByte()
            tag[2] = 'G'.code.toByte()

            // 标题 (30字节)
            val title = "Recorded Audio"
            System.arraycopy(title.toByteArray(), 0, tag, 3, Math.min(title.length, 30))

            // 艺术家 (30字节)
            val artist = "App Recording"
            System.arraycopy(artist.toByteArray(), 0, tag, 33, Math.min(artist.length, 30))

            // 专辑 (30字节)
            val album = "App Recordings"
            System.arraycopy(album.toByteArray(), 0, tag, 63, Math.min(album.length, 30))

            // 年份 (4字节)
            val year = java.util.Calendar.getInstance().get(java.util.Calendar.YEAR).toString()
            System.arraycopy(year.toByteArray(), 0, tag, 93, Math.min(year.length, 4))

            // 注释 (28字节) + 0 + 曲目号
            val comment = "Recorded with AndroidLame"
            System.arraycopy(comment.toByteArray(), 0, tag, 97, Math.min(comment.length, 28))

            // 流派 (1字节)
            tag[127] = 0  // 未知流派

            outputStream.write(tag)
        } catch (e: IOException) {
            e.printStackTrace()
        }
    }
}