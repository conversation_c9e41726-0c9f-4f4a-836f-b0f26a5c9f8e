package com.gankao.conference_assistant_plugin.utils

import java.time.Instant
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter

object TimeUtils {


    fun formatTimestampWithThreeTen(timestamp: Long = System.currentTimeMillis()): String {
        val instant = Instant.ofEpochMilli(timestamp)
        val dateTime = LocalDateTime.ofInstant(instant, ZoneId.systemDefault())
        val formatter = DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")
        return dateTime.format(formatter)
    }
}