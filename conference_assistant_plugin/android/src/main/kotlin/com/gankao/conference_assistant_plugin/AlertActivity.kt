package com.gankao.conference_assistant_plugin

import android.Manifest
import android.R.attr.text
import android.content.pm.PackageManager
import android.icu.lang.UCharacter.GraphemeClusterBreak.L
import android.os.Build.VERSION_CODES.N
import android.os.Bundle
import android.view.Window
import android.view.WindowManager
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import androidx.lifecycle.lifecycleScope
import com.gankao.conference_assistant_plugin.speech.AliSpeechHelper
import com.gankao.conference_assistant_plugin.utils.NotificationHelper
import com.gk.logcat.LogUtil
import com.gk.logcat.LogUtil.context
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.NonCancellable.isActive
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext


class AlertActivity : AppCompatActivity() {

    companion object {
        var instance: AlertActivity? = null
        var shouldShow = true
    }
    override fun onCreate(savedInstanceState: Bundle?) {
        supportRequestWindowFeature(Window.FEATURE_NO_TITLE)
        super.onCreate(savedInstanceState)
        // 设置在锁屏上显示 & 点亮屏幕（用于老设备）
        LogUtil.e("AlertActivity: onCreate shouldShow:$shouldShow")
        if (!shouldShow){
            finish()
            return
        }
        shouldShow = false
        instance = this
        window.addFlags(
            WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON or
                    WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED or
                    WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON or
                    WindowManager.LayoutParams.FLAG_DISMISS_KEYGUARD
        )

        setContentView(R.layout.activity_alert) // 你的自定义布局


        val btnStop = findViewById<TextView>(R.id.stop_button)
        val btnContinue = findViewById<TextView>(R.id.continue_button)

        lifecycleScope.launch {
            (10 downTo 0)
                .forEach {
                    val text = if (it == 0) "结束录音" else "结束录音(${it}s)"
                    withContext(Dispatchers.Main) {
                        btnStop.text = text
                    }
                    delay(1000)
                    if (it == 0){
                        withContext(Dispatchers.Main) {
                            // 停止录音
                            AliSpeechHelper.stopDialogSuspend(isUser = true)
                            if (ActivityCompat.checkSelfPermission(
                                    this@AlertActivity,
                                    Manifest.permission.POST_NOTIFICATIONS
                                ) == PackageManager.PERMISSION_GRANTED
                            ) {
                                NotificationHelper.showNotification(this@AlertActivity, "提示","会议静音超过60秒，AI助理自动结束！")
                            }
                            finish()
                        }
                    }
                }
        }
        btnStop.setOnClickListener {
            lifecycleScope.launch {
                AliSpeechHelper.stopDialogSuspend(isUser = true)
                finish()
            }
        }
        btnContinue.setOnClickListener {
            finish()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        instance = null
    }
}