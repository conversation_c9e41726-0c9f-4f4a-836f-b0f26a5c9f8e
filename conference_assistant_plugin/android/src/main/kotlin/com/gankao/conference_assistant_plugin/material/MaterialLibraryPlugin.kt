package com.gankao.conference_assistant_plugin.material

import android.content.Context
import android.os.Build
import android.os.Handler
import android.os.Looper
import androidx.annotation.NonNull
import com.gankao.conference_assistant_plugin.UploadStatusStreamHandler
import com.gk.logcat.LogUtil
import io.flutter.embedding.engine.plugins.FlutterPlugin
import io.flutter.plugin.common.EventChannel
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugin.common.MethodChannel.MethodCallHandler
import io.flutter.plugin.common.MethodChannel.Result
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch

/** MaterialLibraryPlugin */
private const val TAG = "MaterialLibraryPlugin"

class MaterialLibraryPlugin : FlutterPlugin, MethodCallHandler, CoroutineScope by MainScope() {
    /// The MethodChannel that will handle communication between Flutter and native Android
    private lateinit var channel: MethodChannel
    private lateinit var context: Context

    private lateinit var qiniuHandler: Upload7NiuStreamHandler
    private lateinit var uploadStatusEventChannel: EventChannel

    override fun onAttachedToEngine(flutterPluginBinding: FlutterPlugin.FlutterPluginBinding) {
        context = flutterPluginBinding.applicationContext

        // Setup method channel
        channel = MethodChannel(flutterPluginBinding.binaryMessenger, "material_library_plugin")
        channel.setMethodCallHandler(this)


        // Setup event channel for upload status
        qiniuHandler = Upload7NiuStreamHandler()
//        uploadStatusEventChannel = EventChannel(flutterPluginBinding.binaryMessenger, "com.gankao.conference_assistant_plugin/upload_status")
        uploadStatusEventChannel = EventChannel(flutterPluginBinding.binaryMessenger, "com.gankao.conference_assistant_plugin/qiniu_upload_state")
        uploadStatusEventChannel.setStreamHandler(qiniuHandler)

        // Setup event channels
        setupEventChannels(flutterPluginBinding)

        LogUtil.i(TAG, "MaterialLibraryPlugin attached to engine")


    }

    private fun setupEventChannels(flutterPluginBinding: FlutterPlugin.FlutterPluginBinding) {
        // Upload progress event channel

    }

    override fun onMethodCall(@NonNull call: MethodCall, @NonNull result: Result) {
        when (call.method) {
            "getPlatformVersion" -> {
                result.success("Android ${Build.VERSION.RELEASE}")
            }

            "openFilePicker" -> {
                LogUtil.i(TAG, "openFilePicker8888 called with arguments: ${call.arguments}")
                
                // TODO: 这里需要实现实际的文件选择功能
                // 现在先返回一个测试结果
                val testFiles = listOf<String>(
                    "/storage/emulated/0/test1.txt",
                    "/storage/emulated/0/test2.jpg"
                )
                result.success(testFiles)
            }

            else -> {
                result.notImplemented()
            }
        }
    }

    override fun onDetachedFromEngine(binding: FlutterPlugin.FlutterPluginBinding) {
        channel.setMethodCallHandler(null)
        LogUtil.i(TAG, "MaterialLibraryPlugin detached from engine")
    }
}

/**
 * Stream handler for sending upload status map data to Flutter
 */
class Upload7NiuStreamHandler : EventChannel.StreamHandler {
    private var eventSink: EventChannel.EventSink? = null
    private val mainHandler = Handler(Looper.getMainLooper())

    override fun onListen(arguments: Any?, events: EventChannel.EventSink?) {
        LogUtil.e("UploadStatusStreamHandler : onListen")
        eventSink = events
    }

    override fun onCancel(arguments: Any?) {
        LogUtil.e("UploadStatusStreamHandler : onCancel")
        eventSink = null
    }

    fun sendUploadStatus(statusMap: Map<String, Any>) {
        mainHandler.post {
            if (eventSink != null) {
                LogUtil.e("发送上传状态数据到Flutter: $statusMap")
                eventSink?.success(statusMap)
            } else {
                LogUtil.e("无法发送上传状态数据，eventSink 为 null")
            }
        }
    }
}
